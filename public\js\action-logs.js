// 全局变量
let currentPage = 0;
let pageSize = 20;
let totalRecords = 0;

// DOM 元素
const logsTableBody = document.getElementById('logsTableBody');
const pageStart = document.getElementById('pageStart');
const pageEnd = document.getElementById('pageEnd');
const totalCount = document.getElementById('totalCount');
const prevPageBtn = document.getElementById('prevPage');
const nextPageBtn = document.getElementById('nextPage');
const searchBtn = document.getElementById('searchBtn');
const currentUserDisplay = document.getElementById('currentUserDisplay');

// 权限检查和用户信息获取
function checkAuth() {
    const token = localStorage.getItem('token');

    if (!token) {
        alert('请先登录后访问');
        window.location.href = 'login.html';
        return false;
    }

    return true;
}

// 检查管理员权限
async function checkAdminPermission() {
    const token = localStorage.getItem('token');
    if (!token) return false;

    try {
        const response = await fetch('/api/debug/check-role', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();
        if (data.status === 'success' && data.user.role === 'admin') {
            // 显示用户信息
            currentUserDisplay.innerHTML = `<i class="fas fa-user-shield mr-1"></i>${data.user.username} <span class="text-red-300">(管理员)</span>`;
            return true;
        } else {
            alert('权限不足，只有管理员可以访问操作日志');
            window.location.href = 'pc-components.html';
            return false;
        }
    } catch (error) {
        console.error('权限检查失败:', error);
        alert('权限检查失败，请重新登录');
        window.location.href = 'login.html';
        return false;
    }
}

// 加载操作日志
async function loadActionLogs() {
    if (!checkAuth()) return;

    const token = localStorage.getItem('token');
    const usernameFilter = document.getElementById('usernameFilter').value;
    const actionTypeFilter = document.getElementById('actionTypeFilter').value;
    const targetTableFilter = document.getElementById('targetTableFilter').value;
    const startDateFilter = document.getElementById('startDateFilter').value;
    const endDateFilter = document.getElementById('endDateFilter').value;

    try {
        logsTableBody.innerHTML = '<tr><td colspan="9" class="py-4 text-center">正在加载...</td></tr>';

        // 构建查询参数
        const params = new URLSearchParams({
            limit: pageSize,
            offset: currentPage * pageSize
        });

        if (usernameFilter) params.append('username', usernameFilter);
        if (actionTypeFilter) params.append('actionType', actionTypeFilter);
        if (targetTableFilter) params.append('targetTable', targetTableFilter);
        if (startDateFilter) params.append('startDate', startDateFilter);
        if (endDateFilter) params.append('endDate', endDateFilter);

        console.log('请求操作日志，参数:', params.toString());

        const response = await fetch(`/api/action-logs?${params.toString()}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('获取操作日志:', data);

        // 渲染日志表格
        renderLogs(data.data || []);

        // 更新分页信息
        totalRecords = data.total || 0;
        const start = totalRecords > 0 ? currentPage * pageSize + 1 : 0;
        const end = Math.min(start + pageSize - 1, totalRecords);

        pageStart.textContent = start;
        pageEnd.textContent = end;
        totalCount.textContent = totalRecords;

        // 更新分页按钮状态
        prevPageBtn.disabled = currentPage === 0;
        prevPageBtn.classList.toggle('opacity-50', currentPage === 0);

        const maxPage = Math.ceil(totalRecords / pageSize) - 1;
        nextPageBtn.disabled = currentPage >= maxPage;
        nextPageBtn.classList.toggle('opacity-50', currentPage >= maxPage);

    } catch (error) {
        console.error('加载操作日志失败:', error);
        logsTableBody.innerHTML = `
                    <tr>
                        <td colspan="9" class="py-4 text-center text-red-500">加载日志失败: ${error.message}</td>
                    </tr>
                `;
    }
}

// 渲染日志记录
function renderLogs(logs) {
    if (!logs || logs.length === 0) {
        logsTableBody.innerHTML = '<tr><td colspan="9" class="py-4 text-center">暂无操作日志</td></tr>';
        return;
    }

    let html = '';

    logs.forEach(log => {
        // 处理操作类型显示
        let actionTypeText = '';
        let actionTypeClass = '';

        switch(log.action_type) {
            case 'CREATE':
                actionTypeText = '创建';
                actionTypeClass = 'status-CREATE';
                break;
            case 'UPDATE':
                actionTypeText = '更新';
                actionTypeClass = 'status-UPDATE';
                break;
            case 'DELETE':
                actionTypeText = '删除';
                actionTypeClass = 'status-DELETE';
                break;
            case 'READ':
                actionTypeText = '读取';
                actionTypeClass = 'status-READ';
                break;
            default:
                actionTypeText = log.action_type || '未知';
        }

        // 处理详细信息显示
        let detailsText = '';
        if (log.details) {
            if (typeof log.details === 'object') {
                detailsText = JSON.stringify(log.details, null, 2);
            } else {
                detailsText = log.details;
            }
            // 限制显示长度
            if (detailsText.length > 100) {
                detailsText = detailsText.substring(0, 100) + '...';
            }
        } else {
            detailsText = '-';
        }

        // 处理时间格式
        let formattedTime = '';
        try {
            if (log.created_at) {
                const dateOptions = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                formattedTime = new Date(log.created_at).toLocaleString('zh-CN', dateOptions);
            } else {
                formattedTime = '-';
            }
        } catch (e) {
            console.error('时间格式化错误:', e);
            formattedTime = log.created_at || '-';
        }

        html += `
                    <tr>
                        <td class="py-2 px-4 border-b">${log.id || '-'}</td>
                        <td class="py-2 px-4 border-b">${log.user_id || '-'}</td>
                        <td class="py-2 px-4 border-b">${log.username || '-'}</td>
                        <td class="py-2 px-4 border-b ${actionTypeClass}">${actionTypeText}</td>
                        <td class="py-2 px-4 border-b">${log.target_table || '-'}</td>
                        <td class="py-2 px-4 border-b">${log.target_id || '-'}</td>
                        <td class="py-2 px-4 border-b details-cell" title="${detailsText}">${detailsText}</td>
                        <td class="py-2 px-4 border-b">${log.ip_address || '-'}</td>
                        <td class="py-2 px-4 border-b">${formattedTime}</td>
                    </tr>
                `;
    });

    logsTableBody.innerHTML = html;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('操作日志页面已加载');

    // 检查管理员权限
    const hasPermission = await checkAdminPermission();
    if (!hasPermission) return;

    // 初始加载
    loadActionLogs();

    // 事件监听
    searchBtn.addEventListener('click', () => {
        currentPage = 0; // 重置到第一页
        loadActionLogs();
    });

    // 分页事件
    prevPageBtn.addEventListener('click', () => {
        if (currentPage > 0) {
            currentPage--;
            loadActionLogs();
        }
    });

    nextPageBtn.addEventListener('click', () => {
        const maxPage = Math.ceil(totalRecords / pageSize) - 1;
        if (currentPage < maxPage) {
            currentPage++;
            loadActionLogs();
        }
    });
});