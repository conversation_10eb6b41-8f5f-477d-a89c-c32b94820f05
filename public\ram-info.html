<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内存信息管理 - 电脑配件综合管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/viewerjs/1.11.6/viewer.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/js-pinyin.js"></script>
    <script src="/js/pinyin-wrapper.js"></script>
    <script src="/js/main.js"></script>
    <style>
        /* 简单高效的过渡效果 */
        body {
            transition: background-color 0.25s ease;
        }
        
        .bg-white, .bg-gray-50, .bg-gray-100, 
        .text-gray-700, .text-gray-800, .text-gray-900,
        .border-gray-200, .border-gray-300,
        .ram-card, #ramDetailModal, input, select, textarea, button {
            transition: background-color 0.25s ease, color 0.25s ease, border-color 0.25s ease;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .image-preview {
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        /* 标签样式 */
        .spec-tag {
            padding: 3px 8px !important;
            border-radius: 4px !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            line-height: 1.5 !important;
            display: inline-block !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            max-width: fit-content !important;
            margin: 2px 4px 2px 0 !important;
            transition: all 0.2s ease;
        }

        .spec-tag.socket {
            background-color: rgba(180, 83, 9, 0.1) !important;
            color: #b45309 !important;
            border: 1px solid rgba(180, 83, 9, 0.2) !important;
        }
        
        .spec-tag.cores {
            background-color: rgba(22, 101, 52, 0.1) !important;
            color: #166534 !important;
            border: 1px solid rgba(22, 101, 52, 0.2) !important;
        }
        
        .spec-tag.freq {
            background-color: rgba(76, 29, 149, 0.1) !important;
            color: #4c1d95 !important;
            border: 1px solid rgba(76, 29, 149, 0.2) !important;
        }

        .spec-tag.process {
            background-color: rgba(6, 95, 70, 0.1) !important;
            color: #065f46 !important;
            border: 1px solid rgba(6, 95, 70, 0.2) !important;
        }

        .spec-tag.tdp {
            background-color: rgba(194, 65, 12, 0.1) !important;
            color: #c2410c !important;
            border: 1px solid rgba(194, 65, 12, 0.2) !important;
        }
        
        /* 卡片式布局 */
        .ram-card {
            width: 100% !important;
            max-width: 100% !important;
            margin: 0 0 16px 0 !important;
            box-sizing: border-box !important;
            border: none;
            border-radius: 10px;
            overflow: hidden;
            background-color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            padding: 16px !important;
            display: flex;
            flex-direction: column;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .ram-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.1);
        }
            
        .ram-card-header {
            padding: 12px 16px !important;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            width: 100% !important;
            box-sizing: border-box !important;
        }
            
        .ram-card-body {
            padding: 16px !important;
            min-height: 120px !important;
            width: 100% !important;
            box-sizing: border-box !important;
            overflow: hidden !important;
        }
            
        .ram-card-footer {
            padding: 12px !important;
            background-color: #fcfcfc;
            border-top: 1px solid #eee;
            width: 100% !important;
            box-sizing: border-box !important;
        }
        
        /* 标签组 */
        .tag-group {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin: 8px 0;
            width: 100%;
            box-sizing: border-box !important;
        }
        
        /* 操作按钮样式 */
        .action-btn {
            transition: all 0.2s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-btn:active {
            transform: translateY(1px);
        }

        /* 移动端优化 */
        @media (max-width: 640px) {
            .mobile-stack {
                flex-direction: column;
            }

            .mobile-full-width {
                width: 100%;
            }

            .mobile-p-2 {
                padding: 0.5rem;
            }

            .mobile-text-sm {
                font-size: 0.875rem;
                line-height: 1.25rem;
            }

            .mobile-hidden {
                display: none;
            }

            .mobile-flex-col {
                flex-direction: column;
            }

            .mobile-w-full {
                width: 100%;
            }

            .mobile-mt-2 {
                margin-top: 0.5rem;
            }
            
            /* 新增移动端样式 */
            .table-compact {
                font-size: 0.8rem;
                width: 100%;
                table-layout: fixed;
            }
            
            .table-compact td,
            .table-compact th {
                padding: 0.5rem 0.25rem;
                word-break: break-word;
            }
            
            .btn-group-compact button {
                padding: 0.25rem 0.5rem;
                margin: 0 2px;
            }
            
            /* 改进的移动端内存列表样式 */
            .table-compact td {
                font-size: 0.75rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 120px;
            }
            
            /* 操作按钮样式优化 */
            .action-buttons {
                display: flex;
                gap: 4px;
                flex-wrap: nowrap;
                justify-content: center;
            }
            
            .action-buttons button {
                min-width: 28px;
                height: 28px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            /* 内存型号列宽度调整 */
            .model-column {
                max-width: 80px !important;
                width: 35%;
            }
            
            /* 容量/类型列宽度调整 */
            .memory-column {
                max-width: 60px !important;
                width: 30%;
            }
            
            /* 操作列宽度调整 */
            .action-column {
                width: 35%;
            }
            
            /* 分页控件移动端优化 */
            .pagination-mobile {
                display: flex;
                justify-content: space-between;
                width: 100%;
                align-items: center;
            }
            
            .page-controls-mobile {
                display: flex;
                gap: 2px;
            }
            
            .page-controls-mobile button {
                min-width: 28px;
                height: 28px;
                padding: 0;
                font-size: 0.75rem;
            }
            
            #pageInfo {
                font-size: 0.7rem;
                padding: 2px 4px;
                white-space: nowrap;
            }
            
            /* 确保表格不会水平溢出 */
            .overflow-x-auto {
                max-width: 100vw;
                margin: 0;
                padding: 0;
            }
            
            /* 移动端卡片样式 */
            .ram-card {
                padding: 0 !important;
                margin-bottom: 12px;
            }
            
            .ram-card-header {
                padding: 10px 12px !important;
            }
            
            .ram-card-body {
                padding: 12px !important;
                min-height: auto !important;
            }
            
            .ram-card-footer {
                padding: 10px !important;
            }
            
            /* 移动端操作按钮样式 */
            .action-buttons {
                gap: 24px;
            }
            
            /* 移动端品牌标签 */
            .mobile-table .ram-badge,
            .ram-card .ram-badge {
                padding: 4px 10px !important;
                background-color: rgba(76, 175, 80, 0.1) !important;
                color: #4CAF50 !important;
                border: 1px solid rgba(76, 175, 80, 0.2) !important;
                font-weight: 600 !important;
                border-radius: 4px !important;
                font-size: 0.85rem;
                margin-right: 8px;
            }
            
            /* 移动端模态框优化 */
            #ramDetailModal {
                align-items: flex-start !important;
                padding-top: 0 !important;
                padding-bottom: 0 !important;
                overflow-y: hidden !important;
            }
            
            #ramDetailModal .mobile-modal-content {
                height: 100vh !important;
                max-height: 100vh !important;
                width: 100% !important;
                max-width: 100% !important;
                border-radius: 0 !important;
                overflow-y: auto !important;
                margin: 0 !important;
                padding-bottom: 80px !important; /* 给底部按钮留空间 */
            }
            
            /* 移动端专用关闭按钮 */
            .mobile-close-button {
                display: flex !important;
                position: fixed !important;
                bottom: 20px !important;
                right: 20px !important;
                width: 50px !important;
                height: 50px !important;
                border-radius: 50% !important;
                background-color: rgba(22, 163, 74, 0.9) !important;
                color: white !important;
                align-items: center !important;
                justify-content: center !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
                z-index: 100 !important;
                border: none !important;
            }
        }

        /* 暗黑模式样式 */
        .dark-mode {
            --bg-primary: #121212;
            --bg-secondary: #1e1e1e;
            --bg-card: #252525;
            --bg-input: #2a2a2a;
            --text-primary: #f5f5f5;
            --text-secondary: #a0aec0;
            --border-color: #333333;
            --input-bg: #2a2a2a;
            --input-text: #e2e8f0;
            --button-primary-bg: #16a34a;
            --button-primary-hover: #15803d;
            --button-primary-text: #ffffff;
            --highlight-color: #4ade80;
            --accent-color: #4ade80;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        
        body.dark-mode {
            background-color: var(--bg-primary) !important;
            color: var(--text-primary) !important;
        }
        
        .dark-mode .bg-white {
            background-color: var(--bg-secondary) !important;
        }
        
        .dark-mode .bg-gray-50 {
            background-color: var(--bg-primary) !important;
        }
        
        .dark-mode .bg-gray-100 {
            background-color: #1a1a1a !important;
        }

        .dark-mode .bg-green-50 {
            background-color: rgba(22, 163, 74, 0.1) !important;
        }
        
        .dark-mode .text-gray-700,
        .dark-mode .text-gray-800,
        .dark-mode .text-gray-900 {
            color: var(--text-primary) !important;
        }
        
        .dark-mode .text-gray-500,
        .dark-mode .text-gray-600 {
            color: var(--text-secondary) !important;
        }
        
        .dark-mode .border-gray-200,
        .dark-mode .border-gray-300 {
            border-color: var(--border-color);
        }
        
        .dark-mode input,
        .dark-mode select,
        .dark-mode textarea {
            background-color: var(--input-bg);
            color: var(--input-text);
            border-color: var(--border-color);
        }

        .dark-mode input:focus,
        .dark-mode select:focus,
        .dark-mode textarea:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
        }
        
        .dark-mode .shadow-md {
            box-shadow: var(--card-shadow);
        }

        .dark-mode .text-green-700 {
            color: var(--highlight-color);
        }

        .dark-mode .bg-green-600 {
            background-color: var(--button-primary-bg);
        }

        .dark-mode .hover\:bg-green-700:hover {
            background-color: var(--button-primary-hover);
        }

        /* 表格美化 */
        .dark-mode table thead {
            background-color: #1a1a1a;
        }
        
        .dark-mode table th {
            color: #94a3b8;
        }
        
        .dark-mode table tbody tr {
            border-color: #333;
        }
        
        .dark-mode table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.03);
        }
        
        .dark-mode .divide-y,
        .dark-mode table tbody,
        .dark-mode table tr {
            border-color: #333;
        }
        
        /* 模态框美化 */
        .dark-mode #ramDetailModal .bg-white {
            background-color: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .dark-mode #ramDetailModal .border-b,
        .dark-mode #ramDetailModal .border-t {
             border-color: var(--border-color);
        }

        .dark-mode .bg-blue-50 {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .dark-mode .text-blue-700 {
            color: #60a5fa;
        }

        .dark-mode .text-blue-600 {
            color: #60a5fa;
        }
        
        .dark-mode .bg-green-100 {
            background-color: rgba(22, 163, 74, 0.15);
        }
        .dark-mode .text-green-800 {
            color: #4ade80;
        }
        .dark-mode .border-green-200 {
            border-color: rgba(74, 222, 128, 0.3);
        }

        .dark-mode .bg-purple-100 {
            background-color: rgba(139, 92, 246, 0.15);
        }
        .dark-mode .text-purple-800 {
            color: #a78bfa;
        }
        .dark-mode .border-purple-200 {
            border-color: rgba(167, 139, 250, 0.3);
        }

        .dark-mode .bg-amber-100 {
            background-color: rgba(245, 158, 11, 0.15);
        }
        .dark-mode .text-amber-800 {
            color: #fbbf24;
        }
        .dark-mode .border-amber-200 {
            border-color: rgba(251, 191, 36, 0.3);
        }

        .dark-mode .bg-indigo-100 {
            background-color: rgba(99, 102, 241, 0.15);
        }
        .dark-mode .text-indigo-800 {
            color: #818cf8;
        }
        .dark-mode .border-indigo-200 {
            border-color: rgba(129, 140, 248, 0.3);
        }

        /* 表格和卡片线条移除 */
        .dark-mode .divide-y > :not([hidden]) ~ :not([hidden]),
        .dark-mode thead,
        .dark-mode table tbody tr,
        .dark-mode .border-b,
        .dark-mode .border-t {
            border: none !important;
        }

        .dark-mode .ram-card > div:nth-child(1), /* Card Header */
        .dark-mode .ram-card > div:nth-child(3)  /* Card Footer */
        {
            border: none !important;
        }
        
        /* 重置按钮样式 */
        .dark-mode #resetFilterBtn {
            background-color: var(--bg-input);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .dark-mode #resetFilterBtn:hover {
            background-color: #373737;
            color: var(--text-primary);
        }

        /* 移动端卡片暗色模式 */
        .dark-mode .ram-card {
            background-color: var(--bg-secondary) !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
            border: 1px solid rgba(255, 255, 255, 0.05) !important;
        }

        .dark-mode .ram-card-outer-container {
            margin-bottom: 15px !important;
        }
    </style>
    <script src="/js/permission-helper.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <header class="mb-4 sm:mb-8">
            <h1 class="text-xl sm:text-3xl font-bold text-green-700 flex items-center">
                <i class="fas fa-memory mr-2 sm:mr-3"></i> 内存信息管理
            </h1>
            <div class="flex justify-between items-center">
                <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">记录和管理内存配置信息</p>
                <div class="mt-2 flex space-x-2">
                    <button id="darkModeToggle" class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-700 shadow-sm transition-all duration-300" title="切换暗夜模式">
                        <i class="fas fa-moon"></i>
                    </button>
                    <a href="/pc-components.html"
                        class="inline-block bg-green-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-green-700 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1"></i> 返回配件列表
                    </a>
                    <a href="/"
                        class="inline-block bg-green-600 text-white py-1 sm:py-2 px-3 sm:px-4 rounded-md hover:bg-green-700 text-sm sm:text-base">
                        <i class="fas fa-home mr-1"></i> 首页
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
            <!-- 左侧表单区域 -->
            <div class="lg:col-span-1 bg-white p-3 sm:p-6 rounded-lg shadow-md">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-plus-circle mr-1 sm:mr-2 text-green-500"></i> 添加内存信息
                </h2>

                <form id="ramForm" class="space-y-3 sm:space-y-4">
                    <!-- 智能识别区域 -->
                    <div class="border border-green-200 rounded-md p-3 bg-green-50">
                        <h3 class="text-md font-medium text-green-700 mb-2 flex items-center">
                            <i class="fas fa-magic mr-1 text-green-500"></i> 智能识别
                        </h3>
                        <div class="space-y-2">
                            <textarea id="smartInput" rows="3"
                                class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                placeholder="粘贴内存参数文本，如：品牌：芝奇、型号：皇家戟、类型：DDR4、容量：16GB、频率：3200MHz、CL：16-18-18-38..."></textarea>
                            <button type="button" id="autoFillBtn"
                                class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-sm">
                                <i class="fas fa-magic mr-2"></i>智能识别
                            </button>
                        </div>
                    </div>

                    <!-- 1.内存核心信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">内存核心信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="model" class="block text-sm font-medium text-gray-700 mb-1">内存型号
                                    <span class="text-red-500">*</span></label>
                                <input type="text" id="model"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: Corsair Vengeance RGB Pro" required>
                            </div>

                            <div>
                                <label for="brand" class="block text-sm font-medium text-gray-700 mb-1">品牌 <span
                                        class="text-red-500">*</span></label>
                                <select id="brand"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择品牌</option>
                                    <option value="威刚">威刚 (ADATA)</option>
                                    <option value="雷克沙">雷克沙 (Lexar)</option>
                                    <option value="芝奇">芝奇 (G.Skill)</option>
                                    <option value="宏碁 - 掠夺者">宏碁 - 掠夺者 (Acer - Predator)</option>
                                    <option value="亿储">亿储 (Essence)</option>
                                    <option value="金士顿">金士顿 (Kingston)</option>
                                    <option value="阿斯加特">阿斯加特 (Asgard)</option>
                                    <option value="金百达">金百达 (KingSpec)</option>
                                    <option value="机械师">机械师</option>
                                    <option value="美商海盗船">美商海盗船 (Corsair)</option>
                                    <option value="英睿达">英睿达 (Crucial)</option>
                                    <option value="宇瞻">宇瞻 (Apacer)</option>
                                    <option value="光威">光威 (Gloway)</option>
                                    <option value="十铨">十铨 (Team)</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div>
                                <!-- <label for="capacity" class="block text-sm font-medium text-gray-700 mb-1">内存容量 (GB)
                                    <span class="text-red-500">*</span></label>
                                <input type="number" id="capacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 16" required> -->

                                <label for="capacity" class="block text-sm font-medium text-gray-700 mb-1">内存容量 <span
                                        class="text-red-500">*</span></label>
                                <select id="capacity"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择内存容量</option>
                                    <option value="8">8G</option>
                                    <option value="16">16G</option>
                                    <option value="24">24G</option>
                                    <option value="32">32G</option>
                                    <option value="48">48G</option>

                                    <option value="64">64G</option>
                                    <option value="96">96G</option>
                                    <option value="128">128G</option>

                                </select>
                            </div>

                            <div>
                                <label for="memoryType" class="block text-sm font-medium text-gray-700 mb-1">内存类型 <span
                                        class="text-red-500">*</span></label>
                                <select id="memoryType"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    required>
                                    <option value="">选择内存类型</option>
                                    <option value="DDR5">DDR5</option>
                                    <option value="DDR4">DDR4</option>
                                    <option value="DDR3">DDR3</option>
                                    <option value="DDR2">DDR2</option>
                                </select>
                            </div>

                            <div>
                                <label for="speed" class="block text-sm font-medium text-gray-700 mb-1">频率 (MHz)</label>
                                <input type="number" id="speed"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 3200">
                            </div>
                        </div>
                    </div>

                    <!-- 2.内存规格信息 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">内存规格信息</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="modules" class="block text-sm font-medium text-gray-700 mb-1">内存根数</label>
                                <select id="modules" name="kit_size"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                    <option value="">选择根数</option>
                                    <option value="1">1根</option>
                                    <option value="2">2根</option>
                                    <option value="4">4根</option>
                                    <option value="8">8根</option>
                                </select>
                            </div>

                            <div>
                                <label for="casLatency"
                                    class="block text-sm font-medium text-gray-700 mb-1">CAS延迟</label>
                                <input type="number" step="0.1" id="casLatency"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 16">
                            </div>

                            <div>
                                <label for="voltage" class="block text-sm font-medium text-gray-700 mb-1">电压 (V)</label>
                                <input type="number" step="0.01" id="voltage"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 1.35">
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label for="heatSpreader"
                                        class="block text-sm font-medium text-gray-700 mb-1">散热片</label>
                                    <select id="heatSpreader"
                                        class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                        <option value="">选择</option>
                                        <option value="1">有</option>
                                        <option value="0">无</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="rgbLighting"
                                        class="block text-sm font-medium text-gray-700 mb-1">RGB灯效</label>
                                    <select id="rgbLighting" name="rgb"
                                        class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base">
                                        <option value="">选择</option>
                                        <option value="1">有</option>
                                        <option value="0">无</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                                <input type="text" id="color" name="color"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="如: 黑色">
                            </div>
                        </div>
                    </div>

                    <!-- 3.价格与备注 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">价格与备注</h3>

                        <div class="space-y-3">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">¥</span>
                                    </div>
                                    <input type="number" id="price"
                                        class="w-full pl-7 px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                        placeholder="0.00" min="0" step="0.01">
                                </div>
                            </div>

                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                <textarea id="notes" rows="3"
                                    class="w-full px-2 sm:px-3 py-1 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm sm:text-base"
                                    placeholder="其他需要备注的信息..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 4.图片上传 -->
                    <div class="border border-gray-200 rounded-md p-3">
                        <h3 class="text-md font-medium text-gray-700 mb-2">图片上传</h3>

                        <div
                            class="mt-1 flex justify-center px-4 sm:px-6 pt-3 sm:pt-5 pb-4 sm:pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <div class="flex text-sm text-gray-600 items-center justify-center flex-wrap">
                                    <label for="ramImage"
                                        class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none">
                                        <span>上传图片</span>
                                        <input id="ramImage" name="ramImage" type="file" accept="image/*"
                                            class="sr-only">
                                    </label>
                                    <p class="pl-1 mobile-hidden">或将图片拖拽到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF 格式，大小不超过5MB（将自动转换为WebP格式以提高加载速度）</p>
                            </div>
                        </div>

                        <!-- 上传进度条 -->
                        <div id="uploadProgressContainer" class="mt-2 hidden">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2 overflow-hidden">
                                <div id="uploadProgressBar" class="bg-gradient-to-r from-pink-500 to-pink-600 dark:from-pink-400 dark:to-pink-500 h-3 rounded-full transition-all duration-500 ease-out relative" style="width: 0%">
                                    <!-- 进度条光泽效果 -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-xs">
                                <span id="uploadProgressText" class="text-gray-600 dark:text-gray-400 flex items-center">
                                    <i id="uploadProgressIcon" class="fas fa-upload mr-1"></i>
                                    准备上传...
                                </span>
                                <span id="uploadProgressPercent" class="text-pink-600 dark:text-pink-400 font-medium">0%</span>
                            </div>
                        </div>

                        <div id="imagePreviewContainer" class="mt-2 hidden">
                            <div class="relative inline-block">
                                <img id="imagePreview" src="#" alt="预览图"
                                    class="h-24 sm:h-32 rounded-md shadow image-preview">
                                <button type="button" id="removeImageBtn"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center hover:bg-red-600">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md">
                            <i class="fas fa-save mr-1"></i> 保存内存信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 右侧列表区域 -->
            <div class="lg:col-span-3">
                <!-- 搜索和过滤 -->
                <div class="bg-white p-3 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
                    <h2 class="text-xl font-semibold text-green-800 mb-2 flex items-center">
                        <i class="fas fa-memory mr-2 sm:mr-3"></i> 内存列表
                    </h2>
                    <div class="flex flex-wrap items-center gap-2 sm:gap-4">
                        <div class="flex items-center flex-wrap sm:flex-nowrap w-full gap-2">
                            <div class="relative w-full sm:w-auto sm:flex-1" style="max-width:680px;">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="searchInput" placeholder="搜索内存型号、品牌等..."
                                    class="w-full py-2 pl-10 pr-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            </div>

                            <select id="brandFilter"
                                class="w-auto border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="all">所有品牌</option>
                                <option value="威刚">威刚</option>
                                <option value="雷克沙">雷克沙</option>
                                <option value="芝奇">芝奇</option>
                                <option value="宏碁 - 掠夺者">宏碁 - 掠夺者</option>
                                <option value="亿储">亿储</option>
                                <option value="金士顿">金士顿</option>
                                <option value="阿斯加特">阿斯加特</option>
                                <option value="金百达">金百达</option>
                                <option value="机械师">机械师</option>
                                <option value="美商海盗船">美商海盗船</option>
                                <option value="英睿达">英睿达</option>
                                <option value="宇瞻">宇瞻</option>
                                <option value="光威">光威</option>
                                <option value="十铨">十铨</option>
                                <option value="其他">其他</option>
                            </select>
                            
                            <select id="capacityFilter"
                                class="w-auto border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-8 focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="all">所有容量</option>
                                <option value="8">8GB</option>
                                <option value="16">16GB</option>
                                <option value="24">24GB</option>
                                <option value="32">32GB</option>
                                <option value="48">48GB</option>
                                <option value="64">64GB</option>
                                <option value="96">96GB</option>
                                <option value="128">128GB</option>
                            </select>

                            <button id="resetFilterBtn"
                                class="flex-none bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                                <i class="fas fa-sync-alt mr-1"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 内存列表 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="overflow-x-auto sm:mx-0">
                        <div class="inline-block min-w-full align-middle">
                            <table class="min-w-full divide-y divide-gray-200 table-compact sm:table-auto">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        图片
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider model-column">
                                        内存型号
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        品牌
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider memory-column">
                                        容量/类型
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        频率
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                        CAS延迟
                                    </th>
                                    <th scope="col"
                                            class="px-3 py-2 sm:px-4 sm:py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider action-column">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="ramTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript填充 -->
                                <tr>
                                    <td colspan="7" class="px-3 py-4 text-center text-sm text-gray-500">
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div class="mt-4 px-4 py-4 border-t border-gray-200 sm:px-6 flex flex-wrap justify-between items-center pagination-mobile">
                        <div class="text-sm text-gray-700 mb-2 sm:mb-0" id="totalCount">
                            共 0 条记录
                        </div>
                        <div class="flex items-center space-x-1 sm:space-x-2 page-controls-mobile">
                            <button id="firstPage" title="首页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button id="prevPageBtn" title="上一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            
                            <div id="pageNumbers" class="hidden sm:flex space-x-1">
                                <!-- 页码将通过JavaScript填充 -->
                            </div>
                            
                            <span id="pageInfo" class="px-2 py-1 text-sm whitespace-nowrap">第 <span id="currentPageDisplay">1</span> / <span id="totalPagesDisplay">1</span> 页</span>
                            
                            <button id="nextPageBtn" title="下一页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button id="lastPage" title="尾页"
                                class="px-2 py-1 border border-gray-300 rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                            
                            <div class="hidden sm:flex items-center ml-2">
                                <span class="text-sm">跳转到</span>
                                <input type="number" id="pageJump" min="1" class="w-12 ml-1 px-2 py-1 border border-gray-300 rounded-md text-sm">
                                <button id="goToPage" class="ml-1 px-2 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
                                    确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内存详情模态框 -->
        <div id="ramDetailModal"
            class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mobile-modal-content">
                <div
                    class="px-4 py-3 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
                    <h3 class="text-lg font-semibold text-gray-900">内存详细信息</h3>
                    <button id="closeDetailModalBtn" class="text-gray-400 hover:text-gray-500"
                        onclick="closeRamDetailModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="ramDetailContent" class="p-4">
                    <!-- 内存详情内容将通过JavaScript填充 -->
                </div>
                <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                    <button id="editBtn"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                        <i class="fas fa-edit mr-1"></i> 编辑
                    </button>
                    <button id="deleteBtn"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                        <i class="fas fa-trash mr-1"></i> 删除
                    </button>
                    <button
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        onclick="closeRamDetailModal()">
                        关闭
                    </button>
                </div>
            </div>
            <!-- 添加移动端专用关闭按钮 -->
            <button class="mobile-close-button hidden" onclick="closeRamDetailModal()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- 确认删除模态框 -->
        <div id="deleteConfirmModal"
            class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-4 py-3 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">确认删除</h3>
                </div>
                <div class="p-4">
                    <p class="text-gray-700">您确定要删除这条内存记录吗？此操作无法撤销。</p>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button id="cancelDeleteBtn"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                        <button id="confirmDeleteBtn"
                            class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                            确认删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/ram-info.js"></script>
    <script>
    function closeRamDetailModal() {
        document.getElementById('ramDetailModal').classList.add('hidden');
        document.body.style.overflow = '';
    }
    </script>
    <script src="js/upload-progress.js"></script>
</body>

</html>