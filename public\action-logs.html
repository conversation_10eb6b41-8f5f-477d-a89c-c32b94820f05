<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 电脑配件综合管理后台</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="/js/main.js"></script>
    <link rel="stylesheet" href="/css/action-logs.css"></link>
    <script src="/js/action-logs.js"></script>
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-indigo-600 text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <a href="index.html" class="text-2xl font-bold flex items-center">
                <i class="fas fa-truck mr-2"></i> 电脑配件综合管理后台
            </a>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="hover:text-indigo-200">
                    <i class="fas fa-home mr-1"></i> 首页
                </a>
                <span id="currentUserDisplay" class="text-indigo-200"></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto p-4">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">操作日志</h2>
            
            <!-- 筛选器 -->
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="usernameFilter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
                    <select id="actionTypeFilter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">全部</option>
                        <option value="CREATE">创建</option>
                        <option value="UPDATE">更新</option>
                        <option value="DELETE">删除</option>
                        <option value="READ">读取</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">目标表</label>
                    <input type="text" id="targetTableFilter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                    <input type="date" id="startDateFilter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                    <input type="date" id="endDateFilter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
            </div>
            
            <div class="flex justify-end mb-4">
                <button id="searchBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                    <i class="fas fa-search mr-2"></i> 搜索
                </button>
            </div>
            
            <!-- 日志表格 -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="py-2 px-4 border-b text-left">ID</th>
                            <th class="py-2 px-4 border-b text-left">用户ID</th>
                            <th class="py-2 px-4 border-b text-left">用户名</th>
                            <th class="py-2 px-4 border-b text-left">操作类型</th>
                            <th class="py-2 px-4 border-b text-left">目标表</th>
                            <th class="py-2 px-4 border-b text-left">目标ID</th>
                            <th class="py-2 px-4 border-b text-left">详细信息</th>
                            <th class="py-2 px-4 border-b text-left">IP地址</th>
                            <th class="py-2 px-4 border-b text-left">操作时间</th>
                        </tr>
                    </thead>
                    <tbody id="logsTableBody">
                        <!-- 日志记录将通过JavaScript加载 -->
                        <tr>
                            <td colspan="9" class="py-4 text-center">正在加载...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控制 -->
            <div class="mt-4 flex justify-between items-center">
                <div id="pageInfo" class="text-sm text-gray-600">
                    显示 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条
                </div>
                <div class="flex space-x-2">
                    <button id="prevPage" class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-100">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <button id="nextPage" class="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-100">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>
</body>

</html>
