function showLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'flex';
    }
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// 自定义加载指示器，可以用在页面的特定区域
function showLocalLoading(containerId, message = '加载中...') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
                    <div class="flex justify-center py-6">
                        <div class="spinner mr-3"></div>
                        <span class="text-gray-500">${message}</span>
                    </div>
                `;
    }
}

// 全局变量和数据
let qaData = []; // 存储所有问答数据
let currentPage = 1;
let itemsPerPage = 5;
let currentQaId = null; // 当前查看的问题ID
let allTags = new Set(); // 存储所有标签
let isEditing = false; // 是否处于编辑模式

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
    // 初始化事件监听器
    initEventListeners();

    // 在问题列表区域显示加载中提示
    showLocalLoading('questionsList', '正在加载问题列表...');

    // 从API加载数据（不显示全屏加载状态）
    loadQAData(false);
});

// 从API加载数据
function loadQAData(showFullScreenLoading = true) {
    if (showFullScreenLoading) {
        showLoadingOverlay();
    }

    fetch(`/api/customer-service/qa?page=${currentPage}&limit=${itemsPerPage}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('加载数据失败');
            }
            return response.json();
        })
        .then(data => {
            qaData = data.data;

            // 提取所有标签
            allTags.clear();
            qaData.forEach(qa => {
                qa.tags.forEach(tag => allTags.add(tag));
            });

            // 渲染页面
            renderQuestionsList();
            renderCharts();
            renderPopularTags();
            renderTagFilters();

            if (showFullScreenLoading) {
                hideLoadingOverlay();
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showToast('加载数据失败，使用模拟数据进行展示', 'warning');

            // 加载失败时使用默认数据
            loadMockData();

            if (showFullScreenLoading) {
                hideLoadingOverlay();
            }
        });
}

// 处理表单提交
function handleFormSubmit(e) {
    e.preventDefault();

    // 获取表单数据
    const category = document.getElementById('categorySelect').value;
    const title = document.getElementById('questionTitle').value;
    const question = document.getElementById('questionContent').value;
    const solution = document.getElementById('solutionContent').value;
    const tags = document.getElementById('tags').value;

    // 表单验证
    if (!category || !title || !question || !solution) {
        showToast('请填写必要的字段：分类、标题、问题描述和解决方案', 'error');
        return;
    }

    // 显示加载状态
    showLoadingOverlay();

    // 准备要提交的表单数据
    const formData = new FormData();
    formData.append('category', category);
    formData.append('title', title);
    formData.append('question', question);
    formData.append('solution', solution);
    formData.append('tags', tags);

    // 添加图片（如果有）
    const questionImageInput = document.getElementById('questionImage');
    const solutionImageInput = document.getElementById('solutionImage');

    if (questionImageInput.files && questionImageInput.files[0]) {
        formData.append('questionImage', questionImageInput.files[0]);
    } else if (isEditing && document.getElementById('questionImagePreview').classList.contains('hidden')) {
        // 如果在编辑模式下且预览被隐藏，表示用户删除了图片
        formData.append('removeQuestionImage', 'true');
    }

    if (solutionImageInput.files && solutionImageInput.files[0]) {
        formData.append('solutionImage', solutionImageInput.files[0]);
    } else if (isEditing && document.getElementById('solutionImagePreview').classList.contains('hidden')) {
        // 如果在编辑模式下且预览被隐藏，表示用户删除了图片
        formData.append('removeSolutionImage', 'true');
    }

    // 确定请求URL和方法
    let url = '/api/customer-service/qa';
    let method = 'POST';

    if (isEditing && currentQaId) {
        url = `/api/customer-service/qa/${currentQaId}`;
        method = 'PUT';
    }

    // 发送请求
    fetch(url, {
        method: method,
        body: formData
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('保存失败');
            }
            return response.json();
        })
        .then(data => {
            // 重新加载数据
            loadQAData();

            // 重置表单
            resetForm();

            // 显示成功消息
            showToast(isEditing ? '问题修改成功！' : '问题添加成功！', 'success');

            // 重置编辑状态
            isEditing = false;
        })
        .catch(error => {
            console.error('保存问题失败:', error);
            showToast('保存失败，请重试', 'error');
        })
        .finally(() => {
            hideLoadingOverlay();
        });
}

// 删除问题
function deleteQuestion(qaId) {
    // 显示加载状态
    showLoadingOverlay();

    // 发送删除请求
    fetch(`/api/customer-service/qa/${qaId}`, {
        method: 'DELETE'
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('删除失败');
            }
            return response.json();
        })
        .then(data => {
            // 重新加载数据
            loadQAData();

            // 显示成功消息
            showToast('问题已成功删除', 'success');
        })
        .catch(error => {
            console.error('删除问题失败:', error);
            showToast('删除失败，请重试', 'error');
        })
        .finally(() => {
            hideLoadingOverlay();
        });
}

// 打开问题详情
function openQaDetails(qaId) {
    // 首先，发送请求增加查看次数（"即发即忘"）
    fetch(`/api/customer-service/qa/${qaId}/view`, {method: 'POST'})
        .catch(error => console.error('更新查看次数失败:', error));

    // 显示加载状态
    showLoadingOverlay();

    // 发送请求获取问题详情
    fetch(`/api/customer-service/qa/${qaId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取详情失败');
            }
            return response.json();
        })
        .then(qa => {
            // 更新当前问题ID
            currentQaId = qa.id;

            // 获取分类标签
            const categoryLabel = getCategoryLabel(qa.category);

            // 填充详情内容
            const qaDetails = document.getElementById('qaDetails');
            const modalTitle = document.getElementById('modalTitle');

            modalTitle.textContent = qa.title;

            qaDetails.innerHTML = `
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between mb-2">
                                <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-${getCategoryColor(qa.category)} text-white">
                                    ${categoryLabel}
                                </span>
                                <span class="text-sm text-gray-500">
                                    <i class="fas fa-calendar-alt mr-1"></i> ${formatDate(qa.created_at)}
                                </span>
                            </div>
                            
                            <h4 class="text-lg font-medium text-gray-800 mb-2">问题描述：</h4>
                            <p class="text-gray-700 mb-4 whitespace-pre-line">${qa.question}</p>
                            
                            ${qa.question_image_url ? `
                                <div class="mb-4">
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">问题图片：</h5>
                                    <div class="relative inline-block">
                                        <img src="${qa.question_image_url}" alt="问题图片" class="h-48 rounded-md shadow cursor-pointer qa-image-preview" data-src="${qa.question_image_url}">
                                    </div>
                                </div>
                            ` : ''}
                            
                            <h4 class="text-lg font-medium text-gray-800 mb-2">解决方案：</h4>
                            <p class="text-gray-700 mb-4 whitespace-pre-line">${qa.solution}</p>
                            
                            ${qa.solution_image_url ? `
                                <div class="mb-4">
                                    <h5 class="text-sm font-medium text-gray-700 mb-2">解决方案图片：</h5>
                                    <div class="relative inline-block">
                                        <img src="${qa.solution_image_url}" alt="解决方案图片" class="h-48 rounded-md shadow cursor-pointer qa-image-preview" data-src="${qa.solution_image_url}">
                                    </div>
                                </div>
                            ` : ''}
                            
                            <div class="mt-4">
                                <h5 class="text-sm font-medium text-gray-700 mb-2">标签：</h5>
                                <div class="flex flex-wrap gap-1">
                                    ${qa.tags.map(tag => `
                                        <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                                            ${tag}
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;

            // 显示模态框
            document.getElementById('qaModal').classList.remove('hidden');

            // 添加图片预览点击事件
            const imagePreviewElements = qaDetails.querySelectorAll('.qa-image-preview');
            imagePreviewElements.forEach(img => {
                img.addEventListener('click', function () {
                    const src = this.dataset.src;
                    openImagePreview(src);
                });
            });

            // 修改底部按钮
            const modalBottomBtns = document.querySelector('#qaModal .mt-6');
            if (modalBottomBtns) {
                modalBottomBtns.innerHTML = `
                            <div class="flex justify-end space-x-2">
                                <button id="editQaModalBtn" class="px-3 py-1 sm:px-4 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm sm:text-base">
                                    <i class="fas fa-edit mr-1 sm:mr-2"></i> 编辑问题
                                </button>
                                <button id="deleteQaModalBtn" class="px-3 py-1 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm sm:text-base">
                                    <i class="fas fa-trash-alt mr-1 sm:mr-2"></i> 删除问题
                                </button>
                                <button id="closeQaModalBtn" class="px-3 py-1 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm sm:text-base">
                                    关闭
                                </button>
                            </div>
                        `;

                // 添加事件监听
                document.getElementById('editQaModalBtn').addEventListener('click', function () {
                    handleEditQa();
                });

                document.getElementById('deleteQaModalBtn').addEventListener('click', function () {
                    document.getElementById('qaModal').classList.add('hidden');
                    confirmDeleteQuestion(currentQaId);
                });

                document.getElementById('closeQaModalBtn').addEventListener('click', closeQaModalHandler);
            }
        })
        .catch(error => {
            console.error('获取问题详情失败:', error);
            showToast('获取问题详情失败，请重试', 'error');
        })
        .finally(() => {
            hideLoadingOverlay();
        });
}

// 编辑问题
function editQuestion(qaId) {
    // 显示加载状态
    showLoadingOverlay();

    // 发送请求获取问题详情
    fetch(`/api/customer-service/qa/${qaId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取详情失败');
            }
            return response.json();
        })
        .then(qa => {
            // 设置为编辑模式
            isEditing = true;
            currentQaId = qa.id;

            // 填充表单
            document.getElementById('categorySelect').value = qa.category;
            document.getElementById('questionTitle').value = qa.title;
            document.getElementById('questionContent').value = qa.question;
            document.getElementById('solutionContent').value = qa.solution;
            document.getElementById('tags').value = qa.tags.join(', ');

            // 填充图片预览（如果有）
            if (qa.question_image_url) {
                const questionImgPreview = document.getElementById('questionImagePreviewImg');
                questionImgPreview.src = qa.question_image_url;
                document.getElementById('questionImagePreview').classList.remove('hidden');
            } else {
                document.getElementById('questionImagePreview').classList.add('hidden');
            }

            if (qa.solution_image_url) {
                const solutionImgPreview = document.getElementById('solutionImagePreviewImg');
                solutionImgPreview.src = qa.solution_image_url;
                document.getElementById('solutionImagePreview').classList.remove('hidden');
            } else {
                document.getElementById('solutionImagePreview').classList.add('hidden');
            }

            // 修改提交按钮文本
            const submitBtn = document.querySelector('#qaForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存修改';
            }

            // 滚动到表单
            document.getElementById('qaForm').scrollIntoView({behavior: 'smooth'});

            // 显示提示
            showToast('正在编辑问题，请修改后提交', 'info');
        })
        .catch(error => {
            console.error('获取问题详情失败:', error);
            showToast('获取问题详情失败，请重试', 'error');
        })
        .finally(() => {
            hideLoadingOverlay();
        });
}

// 重置表单
function resetForm() {
    // 重置当前编辑ID和状态
    currentQaId = null;
    isEditing = false;

    // 找到表单并重置
    const qaForm = document.getElementById('qaForm');
    if (qaForm) {
        qaForm.reset();

        // 手动清空所有字段值
        document.getElementById('categorySelect').value = '';
        document.getElementById('questionTitle').value = '';
        document.getElementById('questionContent').value = '';
        document.getElementById('solutionContent').value = '';
        document.getElementById('tags').value = '';

        // 重置图片预览和输入
        resetImageUpload('questionImage', 'questionImagePreview');
        resetImageUpload('solutionImage', 'solutionImagePreview');

        // 清空文件输入
        document.getElementById('questionImage').value = '';
        document.getElementById('solutionImage').value = '';

        // 隐藏预览
        document.getElementById('questionImagePreview').classList.add('hidden');
        document.getElementById('solutionImagePreview').classList.add('hidden');

        // 恢复提交按钮文本
        const submitBtn = document.querySelector('#qaForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存问题';
        }
    }
}

// 显示气泡提示
function showToast(message, type = 'info') {
    // 检查是否已有toast，如果有则移除
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // 添加图标
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle"></i>';
    }

    toast.innerHTML = `${icon}${message}`;
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 3秒后隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// 初始化事件监听器
function initEventListeners() {
    // 表单提交
    const qaForm = document.getElementById('qaForm');
    if (qaForm) {
        qaForm.addEventListener('submit', handleFormSubmit);
    }

    // 问题图片上传和预览
    const questionImage = document.getElementById('questionImage');
    if (questionImage) {
        questionImage.addEventListener('change', function () {
            handleImageUpload(this, 'questionImagePreview', 'questionImagePreviewImg');
        });
    }

    // 移除问题图片
    const removeQuestionImageBtn = document.getElementById('removeQuestionImageBtn');
    if (removeQuestionImageBtn) {
        removeQuestionImageBtn.addEventListener('click', function () {
            resetImageUpload('questionImage', 'questionImagePreview');
        });
    }

    // 解决方案图片上传和预览
    const solutionImage = document.getElementById('solutionImage');
    if (solutionImage) {
        solutionImage.addEventListener('change', function () {
            handleImageUpload(this, 'solutionImagePreview', 'solutionImagePreviewImg');
        });
    }

    // 移除解决方案图片
    const removeSolutionImageBtn = document.getElementById('removeSolutionImageBtn');
    if (removeSolutionImageBtn) {
        removeSolutionImageBtn.addEventListener('click', function () {
            resetImageUpload('solutionImage', 'solutionImagePreview');
        });
    }

    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function () {
            currentPage = 1;
            renderQuestionsList();
        });
    }

    // 回车搜索
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function (e) {
            if (e.key === 'Enter') {
                currentPage = 1;
                renderQuestionsList();
            }
        });
    }

    // 分类筛选
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function () {
            currentPage = 1;
            renderQuestionsList();
        });
    }

    // 分页按钮
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    if (prevPageBtn && nextPageBtn) {
        prevPageBtn.addEventListener('click', function () {
            if (currentPage > 1) {
                currentPage--;
                renderQuestionsList();
            }
        });

        nextPageBtn.addEventListener('click', function () {
            const totalPages = Math.ceil(getFilteredQuestions().length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                renderQuestionsList();
            }
        });
    }

    // 关闭问题详情模态框
    const closeQaModal = document.getElementById('closeQaModal');
    const closeQaModalBtn = document.getElementById('closeQaModalBtn');
    if (closeQaModal && closeQaModalBtn) {
        closeQaModal.addEventListener('click', closeQaModalHandler);
        closeQaModalBtn.addEventListener('click', closeQaModalHandler);
    }

    // 关闭图片查看模态框
    const closeImageModal = document.getElementById('closeImageModal');
    if (closeImageModal) {
        closeImageModal.addEventListener('click', function () {
            document.getElementById('imageModal').classList.add('hidden');
        });
    }

    // 编辑问题按钮
    const editQaBtn = document.getElementById('editQaBtn');
    if (editQaBtn) {
        editQaBtn.addEventListener('click', handleEditQa);
    }
}

// 处理图片上传
function handleImageUpload(inputElement, previewContainerId, previewImgId) {
    const file = inputElement.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            const previewContainer = document.getElementById(previewContainerId);
            const previewImg = document.getElementById(previewImgId);

            previewImg.src = e.target.result;
            previewContainer.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
}

// 重置图片上传
function resetImageUpload(inputId, previewContainerId) {
    document.getElementById(inputId).value = '';
    document.getElementById(previewContainerId).classList.add('hidden');
}

// 获取筛选后的问题
function getFilteredQuestions() {
    const searchText = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;

    // 获取选中的标签
    const selectedTags = Array.from(document.querySelectorAll('.tag-filter.active')).map(el => el.dataset.tag);

    return qaData.filter(qa => {
        // 标题或内容包含搜索文本
        const matchesSearch = searchText === '' ||
            qa.title.toLowerCase().includes(searchText) ||
            qa.question.toLowerCase().includes(searchText) ||
            qa.solution.toLowerCase().includes(searchText) ||
            qa.tags.some(tag => tag.toLowerCase().includes(searchText));

        // 匹配分类
        const matchesCategory = categoryFilter === 'all' || qa.category === categoryFilter;

        // 匹配标签（如果有选中的标签）
        const matchesTags = selectedTags.length === 0 ||
            selectedTags.some(tag => qa.tags.includes(tag));

        return matchesSearch && matchesCategory && matchesTags;
    });
}

// 渲染问题列表
function renderQuestionsList() {
    const filteredQuestions = getFilteredQuestions();
    const questionsList = document.getElementById('questionsList');
    const totalQuestions = document.getElementById('totalQuestions');

    // 更新总数
    totalQuestions.textContent = filteredQuestions.length;

    // 清空列表
    questionsList.innerHTML = '';

    // 计算分页
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredQuestions.length);
    const paginatedQuestions = filteredQuestions.slice(startIndex, endIndex);

    // 检查是否有数据
    if (paginatedQuestions.length === 0) {
        questionsList.innerHTML = '<div class="text-center py-6 text-gray-500">没有找到匹配的问题</div>';
        return;
    }

    // 生成问题卡片
    paginatedQuestions.forEach(qa => {
        const card = document.createElement('div');
        card.className = 'bg-gray-50 rounded-lg p-4 qa-card hover:bg-gray-100';

        const categoryLabel = getCategoryLabel(qa.category);

        card.innerHTML = `
                    <div class="flex flex-col sm:flex-row justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">${qa.title}</h3>
                        <div class="mb-2 sm:mb-0">
                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-${getCategoryColor(qa.category)} text-white">
                                ${categoryLabel}
                            </span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mb-3 line-clamp-2">${qa.question}</p>
                    
                    <div class="flex flex-wrap gap-1 mb-3">
                        ${qa.tags.map(tag => `
                            <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full category-tag">
                                ${tag}
                            </span>
                        `).join('')}
                    </div>
                    
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <div>
                            <i class="fas fa-eye mr-1"></i> ${qa.views} 次查看
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt mr-1"></i> ${formatDate(qa.created_at)}
                        </div>
                    </div>
                    
                    <div class="mt-3 flex justify-end space-x-2">
                        <button class="view-qa-btn px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm" data-id="${qa.id}">
                            <i class="fas fa-eye mr-1"></i> 查看详情
                        </button>
                        <button class="edit-qa-btn px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-sm" data-id="${qa.id}">
                            <i class="fas fa-edit mr-1"></i> 编辑
                        </button>
                        <button class="delete-qa-btn px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm" data-id="${qa.id}">
                            <i class="fas fa-trash-alt mr-1"></i> 删除
                        </button>
                    </div>
                `;

        questionsList.appendChild(card);

        // 添加查看按钮点击事件
        const viewBtn = card.querySelector('.view-qa-btn');
        if (viewBtn) {
            viewBtn.addEventListener('click', function () {
                const qaId = parseInt(this.dataset.id);
                openQaDetails(qaId);
            });
        }

        // 添加编辑按钮点击事件
        const editBtn = card.querySelector('.edit-qa-btn');
        if (editBtn) {
            editBtn.addEventListener('click', function () {
                const qaId = parseInt(this.dataset.id);
                editQuestion(qaId);
            });
        }

        // 添加删除按钮点击事件
        const deleteBtn = card.querySelector('.delete-qa-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function () {
                const qaId = parseInt(this.dataset.id);
                confirmDeleteQuestion(qaId);
            });
        }
    });

    // 更新分页信息
    updatePagination(filteredQuestions.length);
}

// 更新分页信息
function updatePagination(totalItems) {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const currentPageEl = document.getElementById('currentPage');
    const totalPagesEl = document.getElementById('totalPages');
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');

    currentPageEl.textContent = currentPage;
    totalPagesEl.textContent = totalPages;

    // 禁用/启用按钮
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages;

    // 更新按钮样式
    if (prevPageBtn.disabled) {
        prevPageBtn.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
        prevPageBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }

    if (nextPageBtn.disabled) {
        nextPageBtn.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
        nextPageBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }
}

// 关闭问题详情模态框
function closeQaModalHandler() {
    document.getElementById('qaModal').classList.add('hidden');
    currentQaId = null;
    // 重新加载数据以刷新查看次数
    loadQAData(false);
}

// 打开图片预览
function openImagePreview(src) {
    const modalImage = document.getElementById('modalImage');
    modalImage.src = src;
    document.getElementById('imageModal').classList.remove('hidden');
}

// 处理编辑问题
function handleEditQa() {
    // 关闭模态框
    closeQaModalHandler();

    // 编辑当前问题
    if (currentQaId) {
        editQuestion(currentQaId);
    }
}

// 渲染标签过滤器
function renderTagFilters() {
    const tagFiltersContainer = document.getElementById('tagFilters');
    if (!tagFiltersContainer) return;

    // 清空容器
    tagFiltersContainer.innerHTML = '';

    // 添加标签
    Array.from(allTags).forEach(tag => {
        const tagElement = document.createElement('button');
        tagElement.className = 'tag-filter px-2 py-1 text-xs rounded-full bg-gray-200 text-gray-700 hover:bg-indigo-100';
        tagElement.dataset.tag = tag;
        tagElement.textContent = tag;

        tagElement.addEventListener('click', function () {
            this.classList.toggle('active');
            if (this.classList.contains('active')) {
                this.classList.remove('bg-gray-200');
                this.classList.add('bg-indigo-500', 'text-white');
            } else {
                this.classList.remove('bg-indigo-500', 'text-white');
                this.classList.add('bg-gray-200');
            }

            currentPage = 1;
            renderQuestionsList();
        });

        tagFiltersContainer.appendChild(tagElement);
    });
}

// 渲染热门标签
function renderPopularTags() {
    const popularTagsContainer = document.getElementById('popularTags');
    if (!popularTagsContainer) return;

    // 尝试从API获取热门标签
    fetch('/api/customer-service/qa/stats/tags')
        .then(response => response.json())
        .then(tags => {
            // 清空容器
            popularTagsContainer.innerHTML = '';

            // 显示前10个
            const topTags = tags.slice(0, 10);

            // 添加标签
            topTags.forEach(tagInfo => {
                const tagElement = document.createElement('div');
                tagElement.className = 'px-3 py-2 rounded-full bg-indigo-100 text-indigo-800 flex items-center';
                tagElement.innerHTML = `
                            <span class="mr-2">${tagInfo.tag}</span>
                            <span class="bg-indigo-200 text-indigo-800 rounded-full px-2 py-0.5 text-xs">${tagInfo.count}</span>
                        `;

                popularTagsContainer.appendChild(tagElement);
            });
        })
        .catch(error => {
            console.error('获取热门标签失败:', error);
            // 使用本地数据计算热门标签
            renderPopularTagsFromLocal();
        });
}

// 使用本地数据渲染热门标签
function renderPopularTagsFromLocal() {
    const popularTagsContainer = document.getElementById('popularTags');
    if (!popularTagsContainer) return;

    // 清空容器
    popularTagsContainer.innerHTML = '';

    // 计算标签频率
    const tagCounts = {};
    qaData.forEach(qa => {
        qa.tags.forEach(tag => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
    });

    // 按频率排序
    const sortedTags = Object.keys(tagCounts).sort((a, b) => tagCounts[b] - tagCounts[a]);

    // 显示前10个
    const topTags = sortedTags.slice(0, 10);

    // 添加标签
    topTags.forEach(tag => {
        const count = tagCounts[tag];
        const tagElement = document.createElement('div');
        tagElement.className = 'px-3 py-2 rounded-full bg-indigo-100 text-indigo-800 flex items-center';
        tagElement.innerHTML = `
                    <span class="mr-2">${tag}</span>
                    <span class="bg-indigo-200 text-indigo-800 rounded-full px-2 py-0.5 text-xs">${count}</span>
                `;

        popularTagsContainer.appendChild(tagElement);
    });
}

// 渲染图表
function renderCharts() {
    // 获取API数据
    fetch('/api/customer-service/qa/stats/categories')
        .then(response => response.json())
        .then(categoryStats => {
            renderCategoryChart(categoryStats);
        })
        .catch(error => {
            console.error('获取分类统计失败:', error);
            // 使用本地数据渲染
            renderCategoryChartFromLocal();
        });

    // 渲染时间图表
    renderTimeChart();
}

// 从API数据渲染分类图表
function renderCategoryChart(categoryStats) {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) return;

    // 准备图表数据
    const labels = categoryStats.map(stat => getCategoryLabel(stat.category));
    const data = categoryStats.map(stat => stat.count);
    const backgroundColors = categoryStats.map(stat => getCategoryChartColor(stat.category));

    // 检查是否已存在图表实例，如果存在则销毁
    if (window.categoryChart && typeof window.categoryChart.destroy === 'function') {
        window.categoryChart.destroy();
    }

    // 创建图表
    window.categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
}

// 使用本地数据渲染分类图表
function renderCategoryChartFromLocal() {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) return;

    // 计算各分类的问题数量
    const categoryCounts = {};
    qaData.forEach(qa => {
        categoryCounts[qa.category] = (categoryCounts[qa.category] || 0) + 1;
    });

    // 准备图表数据
    const labels = Object.keys(categoryCounts).map(getCategoryLabel);
    const data = Object.values(categoryCounts);
    const backgroundColors = Object.keys(categoryCounts).map(getCategoryChartColor);

    // 检查是否已存在图表实例，如果存在则销毁
    if (window.categoryChart && typeof window.categoryChart.destroy === 'function') {
        window.categoryChart.destroy();
    }

    // 创建图表
    window.categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
}

// 渲染时间图表
function renderTimeChart() {
    const ctx = document.getElementById('questionTimeChart');
    if (!ctx) return;

    // 按月份统计问题数量
    const monthData = {};

    // 获取最近6个月
    const today = new Date();
    for (let i = 5; i >= 0; i--) {
        const d = new Date(today);
        d.setMonth(d.getMonth() - i);
        const monthKey = `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}`;
        monthData[monthKey] = 0;
    }

    // 统计每月的问题数量
    qaData.forEach(qa => {
        const date = new Date(qa.created_at);
        const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

        if (monthData[monthKey] !== undefined) {
            monthData[monthKey]++;
        }
    });

    // 准备图表数据
    const labels = Object.keys(monthData).map(key => {
        const [year, month] = key.split('-');
        return `${year}.${month}`;
    });
    const data = Object.values(monthData);

    // 检查是否已存在图表实例，如果存在则销毁
    if (window.timeChart && typeof window.timeChart.destroy === 'function') {
        window.timeChart.destroy();
    }

    // 创建图表
    window.timeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '问题数量',
                data: data,
                backgroundColor: 'rgba(99, 102, 241, 0.6)',
                borderColor: 'rgba(99, 102, 241, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 获取分类标签
function getCategoryLabel(category) {
    const categoryLabels = {
        'hardware': '硬件问题',
        'software': '软件问题',
        'network': '网络问题',
        'display': '显示问题',
        'power': '电源问题',
        'storage': '存储问题',
        'cooling': '散热问题',
        'compatibility': '兼容性问题',
        'assembly': '组装问题',
        'other': '其它问题'
    };

    return categoryLabels[category] || '未分类';
}

// 获取分类颜色
function getCategoryColor(category) {
    const categoryColors = {
        'hardware': 'blue-600',
        'software': 'green-600',
        'network': 'purple-600',
        'display': 'yellow-600',
        'power': 'red-600',
        'storage': 'indigo-600',
        'cooling': 'teal-600',
        'compatibility': 'pink-600',
        'assembly': 'orange-600',
        'other': 'gray-600'
    };

    return categoryColors[category] || 'gray-600';
}

// 获取分类图表颜色
function getCategoryChartColor(category) {
    const categoryColors = {
        'hardware': 'rgba(37, 99, 235, 0.7)',
        'software': 'rgba(22, 163, 74, 0.7)',
        'network': 'rgba(126, 34, 206, 0.7)',
        'display': 'rgba(202, 138, 4, 0.7)',
        'power': 'rgba(220, 38, 38, 0.7)',
        'storage': 'rgba(79, 70, 229, 0.7)',
        'cooling': 'rgba(13, 148, 136, 0.7)',
        'compatibility': 'rgba(219, 39, 119, 0.7)',
        'assembly': 'rgba(234, 88, 12, 0.7)',
        'other': 'rgba(107, 114, 128, 0.7)'
    };

    return categoryColors[category] || 'rgba(107, 114, 128, 0.7)';
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

// 确认删除问题
function confirmDeleteQuestion(qaId) {
    // 保存要删除的ID
    currentQaId = qaId;

    // 显示确认模态框
    document.getElementById('deleteConfirmModal').classList.remove('hidden');

    // 添加确认按钮事件
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const cancelBtn = document.getElementById('cancelDeleteBtn');

    // 移除之前的事件监听器（防止重复添加）
    const newConfirmBtn = confirmBtn.cloneNode(true);
    const newCancelBtn = cancelBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', function () {
        deleteQuestion(currentQaId);
        document.getElementById('deleteConfirmModal').classList.add('hidden');
        currentQaId = null;
    });

    newCancelBtn.addEventListener('click', function () {
        document.getElementById('deleteConfirmModal').classList.add('hidden');
        currentQaId = null;
    });
}

// 加载模拟数据（当API请求失败时使用）
function loadMockData() {
    const mockData = [
        {
            id: 1,
            category: 'hardware',
            title: '显卡风扇不转怎么办？',
            question: '客户反映新买的RTX 3080显卡，安装后风扇不转，但是显示正常，温度也正常，这是什么问题？',
            solution: '这是正常现象。RTX 30系列显卡采用了零噪音技术，当温度低于一定阈值（通常是50-55°C）时，风扇会停止转动以降低噪音。当显卡开始运行游戏或高负载应用时，温度上升，风扇会自动启动。',
            tags: ['显卡', '散热', '风扇', 'RTX 3080'],
            question_image_url: null,
            solution_image_url: null,
            created_at: '2023-08-15T08:30:00Z',
            views: 128
        },
        {
            id: 2,
            category: 'software',
            title: '如何解决蓝屏问题？',
            question: '客户电脑经常出现蓝屏，错误代码是MEMORY_MANAGEMENT，如何排查和解决？',
            solution: '这通常与内存问题有关。建议客户：1. 运行Windows内存诊断工具（mdsched.exe）；2. 尝试重新插拔内存条，或更换内存插槽；3. 更新主板BIOS和芯片组驱动；4. 检查内存兼容性列表确保内存与主板兼容；5. 如果问题持续，可能需要更换内存条。',
            tags: ['蓝屏', '内存', '系统故障', 'BSOD'],
            question_image_url: null,
            solution_image_url: null,
            created_at: '2023-09-22T14:45:00Z',
            views: 256
        },
        {
            id: 3,
            category: 'power',
            title: '电源有滋滋声，是否正常？',
            question: '客户反映新装机的电源在开机后有轻微的滋滋声，询问是否正常或需要更换？',
            solution: '电源的轻微滋滋声通常是电感线圈振动引起的线圈啸叫（Coil Whine），在高品质电源中也可能出现，通常不影响电源性能和寿命。如果声音很大或伴随明显异味，建议更换电源。可以尝试不同的电源插座或UPS来减轻此问题。',
            tags: ['电源', '噪音', '线圈啸叫'],
            question_image_url: null,
            solution_image_url: null,
            created_at: '2023-10-05T11:20:00Z',
            views: 92
        },
        {
            id: 4,
            category: 'compatibility',
            title: 'DDR5内存是否兼容DDR4主板？',
            question: '客户想升级到DDR5内存，但不知道现有的DDR4主板是否兼容？',
            solution: 'DDR5和DDR4内存在物理接口、工作电压和通信协议上都不兼容。DDR5内存需要专门支持DDR5的主板，无法在DDR4主板上使用。如果客户想升级到DDR5，需要同时更换支持DDR5的主板和CPU。',
            tags: ['内存', 'DDR5', 'DDR4', '兼容性'],
            question_image_url: null,
            solution_image_url: null,
            created_at: '2023-11-12T09:10:00Z',
            views: 187
        },
        {
            id: 5,
            category: 'assembly',
            title: 'CPU安装时需要多大力度？',
            question: '客户是首次装机的新手，担心安装CPU时用力过大会损坏，询问合适的力度。',
            solution: '现代CPU安装通常采用零插拔力（ZIF）插槽，不需要用力按压CPU本身。正确步骤是：1. 抬起CPU插槽拉杆；2. 对齐CPU上的三角形标记与插槽的标记；3. 轻轻放入CPU（应自然落入插槽）；4. 盖上固定框并扣下拉杆。拉杆扣下时会有一定阻力，这是正常的。切勿用力按压CPU顶部，以免损坏针脚。',
            tags: ['CPU', '安装', '装机', '新手'],
            question_image_url: null,
            solution_image_url: null,
            created_at: '2023-12-03T16:35:00Z',
            views: 219
        }
    ];

    // 保存模拟数据
    qaData = mockData;

    // 提取所有标签
    allTags.clear();
    mockData.forEach(qa => {
        qa.tags.forEach(tag => allTags.add(tag));
    });

    // 渲染页面
    renderQuestionsList();
    renderCharts();
    renderPopularTags();
    renderTagFilters();

    showToast('使用模拟数据进行展示', 'warning');
}
