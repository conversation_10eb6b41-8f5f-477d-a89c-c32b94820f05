<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创收管理 - 电脑配件管理系统</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="/css/revenue-management.css">
</head>
<body class="bg-gray-100 min-h-screen transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="bg-blue-600 text-white p-4 shadow-lg">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-chart-line mr-2"></i>
                    创收管理系统
                </h1>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 主题切换按钮 -->
                <button id="themeToggle"
                        class="p-2 rounded-lg hover:bg-blue-500 transition-colors"
                        title="切换主题">
                    <i class="fas fa-moon"></i>
                </button>

                <span id="userInfo" class="text-sm"></span>
                <button onclick="index()" class="bg-green-500 hover:bg-green-600 px-3 py-1 rounded text-sm">
                    <i class="fas fa-home mr-1"></i>
                    首页
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mx-auto p-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">总创收</p>
                        <p id="totalRevenue" class="text-2xl font-bold text-gray-800">¥0.00</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-shopping-cart text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">订单数量</p>
                        <p id="totalOrders" class="text-2xl font-bold text-gray-800">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-store text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">店铺数量</p>
                        <p id="totalShops" class="text-2xl font-bold text-gray-800">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-600 text-sm">平均订单价值</p>
                        <p id="avgOrderValue" class="text-2xl font-bold text-gray-800">¥0.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white p-4 rounded-lg shadow-md mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex flex-wrap items-center gap-4">
                    <button id="addRecordBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        添加记录
                    </button>
                    <button id="exportExcelBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>
                        导出Excel
                    </button>
                    <button id="showChartsBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>
                        数据分析
                    </button>
                    <button id="refreshBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>
                        刷新
                    </button>
                </div>
                
                <!-- 筛选器 -->
                <div class="flex flex-wrap items-center gap-4">
                    <select id="shopFilter" class="border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">所有店铺</option>
                    </select>
                    
                    <input type="date" id="startDate" class="border border-gray-300 rounded-lg px-3 py-2">
                    <span class="text-gray-500">至</span>
                    <input type="date" id="endDate" class="border border-gray-300 rounded-lg px-3 py-2">
                    
                    <button id="filterBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-filter mr-2"></i>
                        筛选
                    </button>
                    <button id="resetFilterBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                        <i class="fas fa-times mr-2"></i>
                        重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" class="rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">店铺</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">图片</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创收金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="revenueTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevPageMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button id="nextPageMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startRecord" class="font-medium">1</span> 到 <span id="endRecord" class="font-medium">10</span> 条，
                            共 <span id="totalRecords" class="font-medium">0</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑记录模态框 -->
    <div id="recordModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">添加创收记录</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="recordForm" class="space-y-4">
                    <input type="hidden" id="recordId">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="shopName" class="block text-sm font-medium text-gray-700 mb-1">店铺名称 *</label>
                            <input type="text" id="shopName" name="shop_name" required
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label for="orderNumber" class="block text-sm font-medium text-gray-700 mb-1">订单号 *</label>
                            <input type="text" id="orderNumber" name="order_number" required
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div>
                        <label for="productInfo" class="block text-sm font-medium text-gray-700 mb-1">产品信息 *</label>
                        <textarea id="productInfo" name="product_info" required rows="3"
                                  placeholder="格式：创收金额+具体型号"
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div>
                        <label for="totalRevenue" class="block text-sm font-medium text-gray-700 mb-1">创收金额 *</label>
                        <input type="number" id="totalRevenueInput" name="total_revenue" step="0.01" min="0" required
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="imageUpload" class="block text-sm font-medium text-gray-700 mb-1">图片上传</label>
                        <input type="file" id="imageUpload" name="image" accept="image/*"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <div id="imagePreview" class="mt-2 hidden">
                            <img id="previewImg" src="" alt="预览" class="max-w-xs max-h-32 object-cover rounded">
                        </div>
                    </div>

                    <div>
                        <label for="revenueNotes" class="block text-sm font-medium text-gray-700 mb-1">创收备注</label>
                        <textarea id="revenueNotes" name="revenue_notes" rows="3"
                                  placeholder="记录额外说明信息"
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" id="saveBtn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            <span class="loading hidden"></span>
                            <span id="saveBtnText">保存</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 数据分析模态框 -->
    <div id="chartsModal" class="modal">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">数据分析</h3>
                    <button id="closeChartsModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">创收趋势图</h4>
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-medium text-gray-800 mb-4">店铺创收对比</h4>
                        <div class="chart-container">
                            <canvas id="shopChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/js/auth.js"></script>
    <script src="/js/revenue-management.js"></script>

    <!-- 主题切换功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化主题切换功能');

            const themeToggle = document.getElementById('themeToggle');
            const html = document.documentElement;

            if (!themeToggle) {
                console.error('未找到主题切换按钮');
                return;
            }

            console.log('找到主题切换按钮，设置事件监听器');

            // 主题切换函数
            function toggleTheme() {
                const isDark = html.classList.contains('dark');
                console.log('当前主题:', isDark ? '深色' : '浅色');

                if (isDark) {
                    // 切换到浅色主题
                    html.classList.remove('dark');
                    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    themeToggle.title = '切换到深色主题';
                    localStorage.setItem('theme', 'light');
                    console.log('已切换到浅色主题');
                } else {
                    // 切换到深色主题
                    html.classList.add('dark');
                    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    themeToggle.title = '切换到浅色主题';
                    localStorage.setItem('theme', 'dark');
                    console.log('已切换到深色主题');
                }
            }

            // 绑定点击事件
            themeToggle.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('主题切换按钮被点击');
                toggleTheme();
            });

            // 初始化主题
            const savedTheme = localStorage.getItem('theme');
            console.log('保存的主题偏好:', savedTheme);

            if (savedTheme === 'dark') {
                html.classList.add('dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                themeToggle.title = '切换到浅色主题';
                console.log('初始化为深色主题');
            } else {
                html.classList.remove('dark');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                themeToggle.title = '切换到深色主题';
                console.log('初始化为浅色主题');
            }
        });
    </script>
</body>
</html>
