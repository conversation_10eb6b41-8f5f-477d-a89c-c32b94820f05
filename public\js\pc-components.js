document.addEventListener('DOMContentLoaded', function () {
    // 立即隐藏管理员专用链接，等待权限检查
    const homeLink = document.getElementById('homeLink');
    const codeGeneratorLink = document.getElementById('codeGeneratorLink');
    homeLink.style.display = 'none';
    codeGeneratorLink.style.display = 'none';

    // 检查用户角色，控制管理员专用功能的显示
    const token = localStorage.getItem('token');
    if (token) {
        try {
            const tokenParts = token.split('.');
            if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                const role = payload.role || 'user';

                // 如果是管理员，显示管理员专用功能
                if (role === 'admin') {
                    homeLink.style.display = 'inline-block';
                    codeGeneratorLink.style.display = 'inline-block';
                }
            }
        } catch (e) {
            console.error('解析token失败:', e);
        }
    }

    // 验证token有效性
    fetch('/api/validate-token', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then(response => response.json())
        .then(data => {
            const role = data.user?.role || 'user';

            // 如果是管理员，显示管理员专用功能
            if (role === 'admin') {
                homeLink.style.display = 'inline-block';
                codeGeneratorLink.style.display = 'inline-block';
            }
        })
        .catch(error => {
            console.error('验证token失败:', error);
        });

    // 获取各组件数量的函数
    async function fetchComponentCounts() {
        try {
            const response = await fetchWithAuth('/api/total-components');

            if (!response.ok) {
                throw new Error('获取组件统计失败');
            }
            const data = await response.json();
            console.log('获取到组件数据:', data);

            // 更新统计数字
            document.getElementById('cpu-count').textContent = data.cpus || 0;
            document.getElementById('ram-count').textContent = data.rams || 0;
            document.getElementById('storage-count').textContent = data.storages || 0;
            document.getElementById('gpu-count').textContent = data.gpus || 0;
            document.getElementById('cooler-count').textContent = data.coolers || 0;
            document.getElementById('fan-count').textContent = data.fans || 0;
            document.getElementById('psu-count').textContent = data.psus || 0;
            document.getElementById('case-count').textContent = data.cases || 0;
            document.getElementById('motherboard-count').textContent = data.motherboards || 0;
            document.getElementById('monitor-count').textContent = data.monitors || 0;
        } catch (error) {
            console.error('获取组件统计失败:', error);
            // 在出错时显示提示
            const statsContainer = document.getElementById('stats-container');
            statsContainer.innerHTML = `
                        <div class="col-span-3 bg-red-100 p-4 rounded-md">
                            <p class="text-red-700">获取组件统计信息失败，请稍后再试</p>
                        </div>
                    `;
        }
    }

    // 调用函数获取数据
    fetchComponentCounts();
});