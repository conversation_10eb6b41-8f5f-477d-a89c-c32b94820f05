/**
 * 收入管理系统 - 前端JavaScript
 */

console.log('收入管理系统JavaScript开始加载...');

// 定义一个变量追踪当前是否有活跃的预览
let activeViewer = null;

// 定义openImageFullscreen函数在全局作用域
function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);

    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 清理可能残留的容器
            const existingContainers = document.querySelectorAll('.viewer-container');
            existingContainers.forEach(container => {
                if (container.parentNode) {
                    container.parentNode.removeChild(container);
                }
            });
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }

    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (window.innerWidth < 640) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }
        },
        hide() {
            // 查看器关闭时清理资源
            try {
                viewer.destroy();
                if (container && container.parentNode) {
                    container.parentNode.removeChild(container);
                }
                activeViewer = null;
            } catch (e) {
                console.error('Error cleaning up viewer:', e);
            }
        }
    });

    // 保存当前活跃的查看器引用
    activeViewer = viewer;

    // 显示查看器
    viewer.show();
}

// 立即将函数暴露到全局作用域
window.openImageFullscreen = openImageFullscreen;

class RevenueManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.currentUser = null;
        this.isAdmin = false;
        this.searchQuery = '';
        this.searchTimeout = null;
        this.filters = {
            shop_name: '',
            start_date: '',
            end_date: ''
        };

        this.init();
    }
    
    async init() {
        try {
            // 检查身份验证
            await this.checkAuthentication();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 加载初始数据
            await this.loadStatistics();
            await this.loadRecords();
            await this.loadShops();
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('系统初始化失败，请刷新页面重试');
        }
    }
    
    async checkAuthentication() {
        const token = getToken();
        if (!token) {
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            return;
        }

        try {
            const response = await fetch('/api/validate-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Token验证失败');
            }

            const data = await response.json();
            if (!data.valid) {
                throw new Error('Token无效');
            }

            this.currentUser = data.user;
            this.isAdmin = this.currentUser.role === 'admin';
            
            // 显示用户信息
            this.displayUserInfo();
            
        } catch (error) {
            console.error('身份验证失败:', error);
            localStorage.removeItem('token');
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        }
    }
    
    displayUserInfo() {
        const userInfo = document.getElementById('userInfo');
        if (userInfo) {
            userInfo.textContent = `${this.currentUser.username} (${this.isAdmin ? '管理员' : '用户'})`;
        }
    }
    
    initEventListeners() {
        try {
            // 添加记录按钮
            document.getElementById('addRecordBtn').addEventListener('click', () => {
                this.showRecordModal();
            });

            // 导出Excel按钮
            document.getElementById('exportExcelBtn').addEventListener('click', () => {
                this.exportExcel();
            });

            // 数据分析按钮
            document.getElementById('showChartsBtn').addEventListener('click', () => {
                this.showChartsModal();
            });

            // 筛选按钮
            document.getElementById('filterBtn').addEventListener('click', () => {
                this.applyFilters();
            });

            // 重置筛选按钮
            document.getElementById('resetFilterBtn').addEventListener('click', () => {
                this.resetFilters();
            });

            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', () => {
                this.refreshData();
            });

            // 搜索输入框（带防抖）
            document.getElementById('searchInput').addEventListener('input', (e) => {
                const query = e.target.value.trim();

                // 清除之前的定时器
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 立即更新搜索状态显示
                this.searchQuery = query;
                this.updateSearchStatus();

                // 设置新的定时器，300ms后执行搜索
                this.searchTimeout = setTimeout(() => {
                    this.currentPage = 1; // 重置到第一页
                    this.loadRecords();
                }, 300);
            });

            // 清除搜索按钮
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', () => {
                    this.clearSearch();
                });
            }

            // 图片上传
            document.getElementById('imageUpload').addEventListener('change', (e) => {
                this.handleImageUpload(e);
            });

            // 移除图片
            document.getElementById('removeImageBtn').addEventListener('click', () => {
                this.removeImage();
            });

            // 模态框关闭按钮
            document.getElementById('closeModal').addEventListener('click', () => {
                this.hideRecordModal();
            });

            document.getElementById('closeChartsModal').addEventListener('click', () => {
                this.hideChartsModal();
            });

            // 详情模态框关闭按钮
            const closeDetailModal = document.getElementById('closeDetailModal');
            if (closeDetailModal) {
                closeDetailModal.addEventListener('click', () => {
                    this.hideDetailModal();
                });
            }

            const detailCloseBtn = document.getElementById('detailCloseBtn');
            if (detailCloseBtn) {
                detailCloseBtn.addEventListener('click', () => {
                    this.hideDetailModal();
                });
            }
        } catch (error) {
            console.error('初始化事件监听器时出错:', error);
            throw error;
        }

        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideRecordModal();
        });
        
        // 表单提交
        document.getElementById('recordForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRecord();
        });
        
        // 图片预览
        document.getElementById('imageUpload').addEventListener('change', (e) => {
            this.previewImage(e.target.files[0]);
        });

        // 产品信息自动计算创收金额
        document.getElementById('productInfo').addEventListener('input', (e) => {
            this.autoCalculateRevenue(e.target.value);
        });
        




        document.getElementById('chartsModal').addEventListener('click', (e) => {
            if (e.target.id === 'chartsModal') {
                this.hideChartsModal();
            }
        });

        document.getElementById('detailModal').addEventListener('click', (e) => {
            if (e.target.id === 'detailModal') {
                this.hideDetailModal();
            }
        });
    }
    
    async loadStatistics() {
        try {
            const response = await sendAuthenticatedRequest('/api/revenue-records/statistics');
            const stats = response.data;
            
            document.getElementById('totalRevenue').textContent = `¥${parseFloat(stats.totalRevenue).toFixed(2)}`;
            document.getElementById('totalOrders').textContent = stats.totalOrders;
            document.getElementById('totalShops').textContent = stats.totalShops;
            document.getElementById('avgOrderValue').textContent = `¥${parseFloat(stats.avgOrderValue).toFixed(2)}`;
            
        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.showError('加载统计数据失败');
        }
    }
    
    showLoading(show = true) {
        const tbody = document.getElementById('revenueTableBody');
        if (show) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="px-6 py-8 text-center">
                        <div class="flex items-center justify-center">
                            <div class="loading mr-2"></div>
                            <span class="text-gray-500">加载中...</span>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    async loadRecords() {
        try {
            this.showLoading(true);

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchQuery,
                ...this.filters
            });

            const response = await sendAuthenticatedRequest(`/api/revenue-records?${params}`);
            const { data: records, pagination } = response;

            this.renderRecordsTable(records);
            this.renderPagination(pagination);

        } catch (error) {
            console.error('加载记录失败:', error);
            this.showError('加载记录失败');
            this.renderRecordsTable([]); // 显示空表格
        }
    }
    
    async loadShops() {
        try {
            const response = await sendAuthenticatedRequest('/api/revenue-records/shops');
            const shops = response.data;
            
            const shopFilter = document.getElementById('shopFilter');
            shopFilter.innerHTML = '<option value="">所有店铺</option>';
            
            shops.forEach(shop => {
                const option = document.createElement('option');
                option.value = shop;
                option.textContent = shop;
                shopFilter.appendChild(option);
            });
            
        } catch (error) {
            console.error('加载店铺列表失败:', error);
        }
    }
    
    renderRecordsTable(records) {
        // 渲染桌面端表格
        this.renderDesktopTable(records);
        // 渲染移动端卡片
        this.renderMobileCards(records);
    }

    renderDesktopTable(records) {
        const tbody = document.getElementById('revenueTableBody');
        tbody.innerHTML = '';

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                        暂无数据
                    </td>
                </tr>
            `;
            return;
        }
        
        records.forEach(record => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            
            const statusClass = record.status === '已做单'
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800';

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.shop_name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.order_number}</td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${record.product_info}">${record.product_info}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${record.image_url ? `<img src="${record.image_url}" alt="产品图片" class="w-12 h-12 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageFullscreen('${record.image_url}')">` : '<span class="text-gray-400">无图片</span>'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">¥${parseFloat(record.total_revenue).toFixed(2)}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
                        ${record.status || '未做单'}
                    </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${record.revenue_notes || ''}">${record.revenue_notes || '-'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${new Date(record.created_at).toLocaleString()}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="revenueManager.showDetail(${record.id})" class="text-green-600 hover:text-green-900 mr-3" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="revenueManager.editRecord(${record.id})" class="text-blue-600 hover:text-blue-900 mr-3" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="revenueManager.deleteRecord(${record.id})" class="text-red-600 hover:text-red-900" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    renderMobileCards(records) {
        const container = document.getElementById('revenueCardsContainer');
        container.innerHTML = '';

        if (records.length === 0) {
            container.innerHTML = `
                <div class="p-8 text-center text-gray-500">
                    <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                    <p>暂无数据</p>
                </div>
            `;
            return;
        }

        records.forEach(record => {
            const statusClass = record.status === '已做单'
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800';

            const card = document.createElement('div');
            card.className = 'revenue-card';
            card.innerHTML = `
                <div class="card-header">
                    <div class="flex-1">
                        <div class="card-title">${record.shop_name}</div>
                        <div class="text-sm text-gray-500 font-mono">${record.order_number}</div>
                    </div>
                    <div class="card-amount">¥${parseFloat(record.total_revenue).toFixed(2)}</div>
                </div>

                <div class="card-meta">
                    <div class="card-meta-item">
                        <span class="card-meta-label">状态</span>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${statusClass} inline-block w-fit">
                            ${record.status || '未做单'}
                        </span>
                    </div>
                    <div class="card-meta-item">
                        <span class="card-meta-label">创建时间</span>
                        <span class="card-meta-value">${new Date(record.created_at).toLocaleDateString()}</span>
                    </div>
                </div>

                <div class="card-content">
                    <div class="card-meta-label mb-1">产品信息</div>
                    <div class="card-product-info">${record.product_info}</div>
                </div>

                ${record.image_url ? `
                    <div class="mb-3">
                        <img src="${record.image_url}" alt="产品图片" class="card-image cursor-pointer hover:opacity-90 transition-opacity" onclick="openImageFullscreen('${record.image_url}')">
                    </div>
                ` : ''}

                ${record.revenue_notes ? `
                    <div class="mb-3">
                        <div class="card-meta-label mb-1">备注</div>
                        <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded">${record.revenue_notes}</div>
                    </div>
                ` : ''}

                <div class="card-footer">
                    <div class="card-time">${new Date(record.created_at).toLocaleString()}</div>
                    <div class="card-actions">
                        <button onclick="revenueManager.showDetail(${record.id})" class="card-action-btn card-action-detail" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="revenueManager.editRecord(${record.id})" class="card-action-btn card-action-edit" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="revenueManager.deleteRecord(${record.id})" class="card-action-btn card-action-delete" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(card);
        });
    }

    renderPagination(pagination) {
        const { page, totalPages, total } = pagination;
        
        // 更新记录信息
        const startRecord = (page - 1) * this.pageSize + 1;
        const endRecord = Math.min(page * this.pageSize, total);
        
        document.getElementById('startRecord').textContent = startRecord;
        document.getElementById('endRecord').textContent = endRecord;
        document.getElementById('totalRecords').textContent = total;
        
        // 生成分页按钮
        const paginationContainer = document.getElementById('pagination');
        paginationContainer.innerHTML = '';
        
        // 上一页按钮
        const prevBtn = this.createPaginationButton('上一页', page - 1, page <= 1);
        paginationContainer.appendChild(prevBtn);
        
        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPaginationButton(i, i, false, i === page);
            paginationContainer.appendChild(pageBtn);
        }
        
        // 下一页按钮
        const nextBtn = this.createPaginationButton('下一页', page + 1, page >= totalPages);
        paginationContainer.appendChild(nextBtn);
    }
    
    createPaginationButton(text, pageNum, disabled = false, active = false) {
        const button = document.createElement('button');
        button.textContent = text;
        button.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
            active 
                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
                : disabled 
                    ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
        }`;
        
        if (!disabled && !active) {
            button.addEventListener('click', () => {
                this.currentPage = pageNum;
                this.loadRecords();
            });
        }
        
        return button;
    }
    
    showRecordModal(record = null) {
        const modal = document.getElementById('recordModal');
        const form = document.getElementById('recordForm');
        const title = document.getElementById('modalTitle');

        // 重置表单
        form.reset();
        document.getElementById('recordId').value = '';

        // 重置图片预览
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }

        // 清空文件输入
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.value = '';
        }

        // 重置removeImage标记
        const removeImageInput = document.getElementById('removeImageInput');
        if (removeImageInput) {
            removeImageInput.remove();
        }

        if (record) {
            // 编辑模式
            title.textContent = '编辑收入记录';
            document.getElementById('recordId').value = record.id;
            document.getElementById('shopName').value = record.shop_name;
            document.getElementById('orderNumber').value = record.order_number;
            document.getElementById('productInfo').value = record.product_info;
            document.getElementById('totalRevenueInput').value = record.total_revenue;
            document.getElementById('revenueNotes').value = record.revenue_notes || '';

            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.value = record.status || '未做单';
            }

            // 处理图片预览
            if (record.image_url) {
                const imagePreviewContainer = document.getElementById('imagePreviewContainer');
                const imagePreview = document.getElementById('imagePreview');

                if (imagePreviewContainer && imagePreview) {
                    imagePreview.src = record.image_url;
                    imagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    imagePreview.onclick = () => {
                        openImageFullscreen(record.image_url);
                    };
                }
            }
        } else {
            // 添加模式
            title.textContent = '添加收入记录';
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.value = '未做单'; // 默认状态
            }
        }

        modal.classList.add('show');
    }

    hideRecordModal() {
        const modal = document.getElementById('recordModal');
        modal.classList.remove('show');
    }

    validateForm() {
        const shopName = document.getElementById('shopName').value.trim();
        const orderNumber = document.getElementById('orderNumber').value.trim();
        const productInfo = document.getElementById('productInfo').value.trim();
        const totalRevenue = document.getElementById('totalRevenueInput').value;

        if (!shopName) {
            this.showError('请输入店铺名称');
            return false;
        }

        if (!orderNumber) {
            this.showError('请输入订单号');
            return false;
        }

        if (!productInfo) {
            this.showError('请输入产品信息');
            return false;
        }

        if (totalRevenue === '' || isNaN(parseFloat(totalRevenue)) || parseFloat(totalRevenue) < 0) {
            this.showError('请输入有效的创收金额（可以为0）');
            return false;
        }

        return true;
    }

    async saveRecord() {
        console.log('开始保存记录...');

        // 验证表单
        if (!this.validateForm()) {
            console.log('表单验证失败');
            return;
        }

        const form = document.getElementById('recordForm');
        const formData = new FormData(form);
        const recordId = document.getElementById('recordId').value;

        // 检查status字段的值
        const statusElement = document.getElementById('status');
        const statusValue = statusElement ? statusElement.value : 'NOT_FOUND';
        console.log('Status元素:', statusElement);
        console.log('Status值:', statusValue);
        console.log('FormData中的status:', formData.get('status'));

        // 打印所有FormData内容
        console.log('=== FormData 完整内容 ===');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}:`, value);
        }
        console.log('=== FormData 结束 ===');

        console.log('表单数据:', {
            recordId,
            shop_name: formData.get('shop_name'),
            order_number: formData.get('order_number'),
            product_info: formData.get('product_info'),
            total_revenue: formData.get('total_revenue'),
            status: formData.get('status'),
            revenue_notes: formData.get('revenue_notes'),
            image: formData.get('image') ? 'file selected' : 'no file'
        });

        const saveBtn = document.getElementById('saveBtn');
        const saveBtnText = document.getElementById('saveBtnText');
        const loading = saveBtn.querySelector('.loading');

        // 显示上传进度条
        if (typeof showUploadProgress === 'function') {
            showUploadProgress();
        }

        // 显示加载状态
        loading.classList.remove('hidden');
        saveBtnText.textContent = '保存中...';
        saveBtn.disabled = true;

        try {
            const url = recordId ? `/api/revenue-records/${recordId}` : '/api/revenue-records';
            const method = recordId ? 'PUT' : 'POST';
            const token = getToken();

            console.log('发送请求:', { url, method, token: token ? 'exists' : 'missing' });

            // 使用带进度的上传函数
            const result = await uploadWithProgress(url, method, formData);
            console.log('响应结果:', result);

            if (result.success) {
                this.showSuccess(result.message);
                this.hideRecordModal();
                await this.loadRecords();
                await this.loadStatistics();
                await this.loadShops();
            } else {
                this.showError(result.message);
            }

        } catch (error) {
            console.error('保存记录失败:', error);
            this.showError('保存记录失败');
        } finally {
            // 隐藏上传进度条
            if (typeof hideUploadProgress === 'function') {
                hideUploadProgress();
            }

            // 恢复按钮状态
            loading.classList.add('hidden');
            saveBtnText.textContent = '保存';
            saveBtn.disabled = false;
        }
    }

    async showDetail(id) {
        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records?page=1&limit=1000`);
            const record = response.data.find(r => r.id === id);

            if (record) {
                this.showDetailModal(record);
            } else {
                this.showError('记录不存在');
            }

        } catch (error) {
            console.error('获取记录失败:', error);
            this.showError('获取记录失败');
        }
    }

    showDetailModal(record) {
        // 填充详情数据
        document.getElementById('detailShopName').textContent = record.shop_name;
        document.getElementById('detailOrderNumber').textContent = record.order_number;
        document.getElementById('detailTotalRevenue').textContent = `¥${parseFloat(record.total_revenue).toFixed(2)}`;
        document.getElementById('detailProductInfo').textContent = record.product_info;
        document.getElementById('detailRevenueNotes').textContent = record.revenue_notes || '暂无备注';
        document.getElementById('detailCreatedAt').textContent = new Date(record.created_at).toLocaleString();
        document.getElementById('detailUpdatedAt').textContent = new Date(record.updated_at).toLocaleString();

        // 处理状态显示
        const statusElement = document.getElementById('detailStatus');
        const status = record.status || '未做单';
        statusElement.textContent = status;
        if (status === '已做单') {
            statusElement.className = 'px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
        } else {
            statusElement.className = 'px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800';
        }

        // 处理图片显示
        const detailImage = document.getElementById('detailImage');
        const detailNoImage = document.getElementById('detailNoImage');

        if (record.image_url) {
            detailImage.src = record.image_url;
            detailImage.classList.remove('hidden');
            detailNoImage.classList.add('hidden');

            // 添加点击预览功能
            detailImage.onclick = () => {
                openImageFullscreen(record.image_url);
            };
        } else {
            detailImage.classList.add('hidden');
            detailNoImage.classList.remove('hidden');
        }

        // 绑定编辑按钮事件
        document.getElementById('detailEditBtn').onclick = () => {
            this.hideDetailModal();
            this.editRecord(record.id);
        };

        // 显示模态框
        document.getElementById('detailModal').classList.add('show');
    }

    hideDetailModal() {
        document.getElementById('detailModal').classList.remove('show');
    }

    async editRecord(id) {
        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records?page=1&limit=1000`);
            const record = response.data.find(r => r.id === id);

            if (record) {
                this.showRecordModal(record);
            } else {
                this.showError('记录不存在');
            }

        } catch (error) {
            console.error('获取记录失败:', error);
            this.showError('获取记录失败');
        }
    }

    async deleteRecord(id) {
        if (!confirm('确定要删除这条记录吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await sendAuthenticatedRequest(`/api/revenue-records/${id}`, 'DELETE');

            if (response.success) {
                this.showSuccess(response.message);
                await this.loadRecords();
                await this.loadStatistics();
                await this.loadShops();
            } else {
                this.showError(response.message);
            }

        } catch (error) {
            console.error('删除记录失败:', error);
            this.showError('删除记录失败');
        }
    }

    previewImage(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('imagePreview').classList.remove('hidden');
            document.getElementById('previewImg').src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    applyFilters() {
        this.filters.shop_name = document.getElementById('shopFilter').value;
        this.filters.start_date = document.getElementById('startDate').value;
        this.filters.end_date = document.getElementById('endDate').value;

        this.currentPage = 1;
        this.loadRecords();
    }

    resetFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('shopFilter').value = '';
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';

        this.searchQuery = '';
        this.filters = {
            shop_name: '',
            start_date: '',
            end_date: ''
        };

        this.currentPage = 1;
        this.updateSearchStatus();
        this.loadRecords();
    }

    updateSearchStatus() {
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        const searchResultsText = document.getElementById('searchResultsText');

        if (this.searchQuery) {
            if (searchResultsText) {
                searchResultsText.innerHTML = `<i class="fas fa-search mr-1"></i>搜索 "${this.searchQuery}" 的结果`;
            }
            if (searchResultsInfo) {
                searchResultsInfo.classList.remove('hidden');
            }
        } else {
            if (searchResultsInfo) {
                searchResultsInfo.classList.add('hidden');
            }
        }
    }

    clearSearch() {
        document.getElementById('searchInput').value = '';
        this.searchQuery = '';
        this.currentPage = 1;
        this.updateSearchStatus();
        this.loadRecords();
    }

    async refreshData() {
        try {
            await Promise.all([
                this.loadStatistics(),
                this.loadRecords(),
                this.loadShops()
            ]);
            this.showSuccess('数据刷新成功');
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showError('刷新数据失败');
        }
    }

    async exportExcel() {
        try {
            const params = new URLSearchParams(this.filters);
            const url = `/api/revenue-records/export?${params}`;

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `收入记录_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(downloadUrl);

                this.showSuccess('Excel文件导出成功');
            } else {
                this.showError('导出失败');
            }

        } catch (error) {
            console.error('导出Excel失败:', error);
            this.showError('导出Excel失败');
        }
    }



    async showChartsModal() {
        const modal = document.getElementById('chartsModal');
        modal.classList.add('show');

        try {
            const response = await sendAuthenticatedRequest('/api/revenue-records/charts');
            const { trend, shops } = response.data;

            this.renderRevenueChart(trend);
            this.renderShopChart(shops);

        } catch (error) {
            console.error('加载图表数据失败:', error);
            this.showError('加载图表数据失败');
        }
    }

    hideChartsModal() {
        const modal = document.getElementById('chartsModal');
        modal.classList.remove('show');
    }

    updateChartsTheme() {
        // 更新图表主题，当主题切换时调用
        if (this.revenueChart) {
            const isDarkTheme = document.documentElement.classList.contains('dark');
            const currentTheme = {
                textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
                gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
                tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
            };

            // 更新创收趋势图主题
            this.revenueChart.options.plugins.legend.labels.color = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.backgroundColor = currentTheme.tooltipBg;
            this.revenueChart.options.plugins.tooltip.titleColor = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.bodyColor = currentTheme.textColor;
            this.revenueChart.options.plugins.tooltip.borderColor = currentTheme.tooltipBorder;

            this.revenueChart.options.scales.x.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.x.ticks.color = currentTheme.textColor;
            this.revenueChart.options.scales.x.grid.color = currentTheme.gridColor;

            this.revenueChart.options.scales.y.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.y.ticks.color = currentTheme.textColor;
            this.revenueChart.options.scales.y.grid.color = currentTheme.gridColor;

            this.revenueChart.options.scales.y1.title.color = currentTheme.textColor;
            this.revenueChart.options.scales.y1.ticks.color = currentTheme.textColor;

            this.revenueChart.update();
        }

        if (this.shopChart) {
            const isDarkTheme = document.documentElement.classList.contains('dark');
            const currentTheme = {
                textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
                borderColor: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
                tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
            };

            // 更新店铺分布图主题
            this.shopChart.options.plugins.legend.labels.color = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.backgroundColor = currentTheme.tooltipBg;
            this.shopChart.options.plugins.tooltip.titleColor = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.bodyColor = currentTheme.textColor;
            this.shopChart.options.plugins.tooltip.borderColor = currentTheme.tooltipBorder;

            // 更新边框颜色
            this.shopChart.data.datasets[0].borderColor = currentTheme.borderColor;

            this.shopChart.update();
        }
    }

    renderRevenueChart(trendData) {
        const ctx = document.getElementById('revenueChart').getContext('2d');

        // 销毁现有图表
        if (this.revenueChart) {
            this.revenueChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            gridColor: isDarkTheme ? '#374151' : '#e5e7eb',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        // 处理日期数据，确保正确的时间格式
        const processedData = trendData.map(item => ({
            x: new Date(item.date),
            revenue: parseFloat(item.revenue),
            orders: item.orders
        }));

        const revenues = processedData.map(item => ({ x: item.x, y: item.revenue }));
        const orders = processedData.map(item => ({ x: item.x, y: item.orders }));

        this.revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [{
                    label: '收入金额',
                    data: revenues,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y'
                }, {
                    label: '订单数量',
                    data: orders,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            title: function(context) {
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleDateString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit'
                                });
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'MM-dd'
                            }
                        },
                        display: true,
                        title: {
                            display: true,
                            text: '日期',
                            color: currentTheme.textColor
                        },
                        ticks: {
                            color: currentTheme.textColor,
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '收入金额 (¥)',
                            color: currentTheme.textColor
                        },
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            color: currentTheme.gridColor
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '订单数量',
                            color: currentTheme.textColor
                        },
                        ticks: {
                            color: currentTheme.textColor
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    renderShopChart(shopData) {
        const ctx = document.getElementById('shopChart').getContext('2d');

        // 销毁现有图表
        if (this.shopChart) {
            this.shopChart.destroy();
        }

        // 获取当前主题
        const isDarkTheme = document.documentElement.classList.contains('dark');
        const currentTheme = {
            textColor: isDarkTheme ? '#f9fafb' : '#1f2937',
            borderColor: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBg: isDarkTheme ? '#374151' : '#ffffff',
            tooltipBorder: isDarkTheme ? '#4b5563' : '#e5e7eb'
        };

        const labels = shopData.map(item => item.shop_name);
        const revenues = shopData.map(item => parseFloat(item.revenue));

        // 生成颜色
        const colors = [
            'rgba(59, 130, 246, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(236, 72, 153, 0.8)',
            'rgba(20, 184, 166, 0.8)',
            'rgba(251, 146, 60, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(168, 85, 247, 0.8)'
        ];

        this.shopChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: revenues,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: currentTheme.borderColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: currentTheme.textColor,
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: currentTheme.tooltipBg,
                        titleColor: currentTheme.textColor,
                        bodyColor: currentTheme.textColor,
                        borderColor: currentTheme.tooltipBorder,
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ¥${value.toFixed(2)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }



    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // 根据类型设置样式
        switch (type) {
            case 'success':
                notification.className += ' bg-green-500 text-white';
                break;
            case 'error':
                notification.className += ' bg-red-500 text-white';
                break;
            case 'warning':
                notification.className += ' bg-yellow-500 text-white';
                break;
            default:
                notification.className += ' bg-blue-500 text-white';
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-1">${message}</div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 动画显示
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // 自动消失
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // 自动计算创收金额
    autoCalculateRevenue(productInfo) {
        if (!productInfo) {
            return;
        }

        // 正则表达式匹配各种金额格式
        const patterns = [
            // 匹配 +数字 格式 (如: +108, +20, +30)
            /\+(\d+(?:\.\d+)?)/g,
            // 匹配 数字元 格式 (如: 108元, 20元)
            /(\d+(?:\.\d+)?)元/g,
            // 匹配 ¥数字 格式 (如: ¥108, ¥20)
            /¥(\d+(?:\.\d+)?)/g,
            // 匹配 数字.数字 格式 (如: 108.50, 20.00)
            /(\d+\.\d+)/g
        ];

        let totalRevenue = 0;
        let foundAmounts = [];

        // 尝试每个正则表达式模式
        for (const pattern of patterns) {
            const matches = productInfo.match(pattern);
            if (matches) {
                for (const match of matches) {
                    // 提取数字部分
                    const numberMatch = match.match(/(\d+(?:\.\d+)?)/);
                    if (numberMatch) {
                        const amount = parseFloat(numberMatch[1]);
                        if (!isNaN(amount) && amount >= 0) {
                            foundAmounts.push(amount);
                            totalRevenue += amount;
                        }
                    }
                }
                // 如果找到了匹配项，就不再尝试其他模式
                if (foundAmounts.length > 0) {
                    break;
                }
            }
        }

        // 更新创收金额字段
        const totalRevenueInput = document.getElementById('totalRevenueInput');
        if (totalRevenueInput && totalRevenue >= 0) {
            totalRevenueInput.value = totalRevenue.toFixed(2);
        }
    }

    // 处理图片上传
    handleImageUpload(e) {
        console.log('上传产品图片');

        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            this.showError('请选择有效的图片文件（JPG, PNG, GIF, WEBP）');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 验证文件大小（最大2MB）
        if (file.size > 2 * 1024 * 1024) {
            this.showError('图片文件大小不能超过2MB');
            e.target.value = ''; // 清除文件选择
            return;
        }

        // 显示图片预览
        const reader = new FileReader();
        reader.onload = (event) => {
            const imagePreviewContainer = document.getElementById('imagePreviewContainer');
            const imagePreview = document.getElementById('imagePreview');

            if (imagePreviewContainer && imagePreview) {
                imagePreview.src = event.target.result;
                imagePreviewContainer.classList.remove('hidden');

                // 添加点击预览功能
                imagePreview.onclick = () => {
                    openImageFullscreen(event.target.result);
                };
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    removeImage() {
        console.log('移除产品图片');

        // 清空文件输入
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.value = '';
        }

        // 隐藏预览
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');
        if (imagePreviewContainer) {
            imagePreviewContainer.classList.add('hidden');
        }

        // 清除图片URL（如果是编辑模式）
        const recordId = document.getElementById('recordId');
        if (recordId && recordId.value) {
            // 在编辑模式下，添加隐藏字段标记图片已被移除
            let removeImageInput = document.getElementById('removeImageInput');
            if (!removeImageInput) {
                removeImageInput = document.createElement('input');
                removeImageInput.type = 'hidden';
                removeImageInput.id = 'removeImageInput';
                removeImageInput.name = 'removeImage';
                document.getElementById('recordForm').appendChild(removeImageInput);
            }
            removeImageInput.value = 'true';
        }
    }

}

// 全局实例
let revenueManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    revenueManager = new RevenueManagement();
});

// 全局函数
function gohome() {
    localStorage.removeItem('token');
    window.location.href = '/index.html';
}
