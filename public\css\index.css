.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 移动端优化 */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-p-2 {
        padding: 0.5rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-flex-col {
        flex-direction: column;
    }

    .mobile-w-full {
        width: 100%;
    }

    .mobile-mt-2 {
        margin-top: 0.5rem;
    }
}

/* 移动端样式 */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }

    /* 配件明细表格样式 */
    #detailPartsTableBody td,
    #editPartsTableBody td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    /* 输入框样式 */
    .part-type,
    .part-model,
    .part-quantity,
    .part-price {
        width: 100%;
        min-width: 60px;
    }

    /* 表格滚动容器 */
    .overflow-x-auto {
        margin: 0 -1rem;
        padding: 0 1rem;
    }

    /* 配件明细表格布局 */
    .min-w-full {
        min-width: 600px;
        /* 确保在移动端可以横向滚动 */
    }
}