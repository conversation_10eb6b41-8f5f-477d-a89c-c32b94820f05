# 主题切换功能说明

## 功能概述
为电脑配件综合管理系统的index页面添加了主题切换功能，支持明亮模式和暗黑模式之间的切换。

## 功能特点

### 1. 主题切换按钮
- 位置：页面右上角，在页面标题下方
- 图标：明亮模式显示月亮图标，暗黑模式显示太阳图标
- 交互：点击按钮可在两种主题间切换
- 动画：图标在暗黑模式下会旋转180度

### 2. 主题持久化
- 用户选择的主题会保存在localStorage中
- 页面刷新后会自动应用上次选择的主题
- 默认主题为明亮模式

### 3. 样式适配
已为以下元素添加了暗色主题支持：

#### 基础布局
- 页面背景：灰色50 → 灰色900
- 卡片背景：白色 → 灰色800
- 文字颜色：灰色800 → 灰色200

#### 表单元素
- 输入框：白色背景 → 灰色700背景
- 选择框：白色背景 → 灰色700背景
- 文本区域：白色背景 → 灰色700背景
- 边框颜色：灰色300 → 灰色600

#### 表格
- 表头背景：灰色50 → 灰色700
- 表格行：白色 → 灰色800
- 分割线：灰色200 → 灰色700

#### 模态框
- 背景：白色 → 灰色800
- 标题：灰色900 → 灰色100
- 内容文字：灰色700 → 灰色300

#### 图表区域
- 背景：白色 → 灰色800
- 标题：灰色800 → 灰色200
- 图标：保持品牌色，在暗色主题下稍微调亮

### 4. 用户体验优化
- 主题切换时有平滑的过渡动画（0.3秒）
- 按钮有悬停效果和轻微的缩放动画
- 滚动条在暗色主题下也有相应的样式调整

## 技术实现

### HTML结构
```html
<!-- 主题切换按钮 -->
<button id="themeToggle" 
    class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
    title="切换主题">
    <i id="themeIcon" class="fas fa-moon text-sm"></i>
</button>
```

### JavaScript功能
- ThemeManager类管理主题状态
- 自动检测并应用保存的主题
- 提供主题切换和图标更新功能
- 支持localStorage持久化

### CSS样式
- 使用Tailwind CSS的dark:前缀实现暗色主题
- 配置darkMode: 'class'模式
- 添加自定义CSS动画和过渡效果

## 使用方法
1. 打开index页面
2. 点击右上角的主题切换按钮（月亮/太阳图标）
3. 页面会立即切换到对应主题
4. 主题选择会自动保存，下次访问时生效

## 兼容性
- 支持所有现代浏览器
- 响应式设计，在移动端和桌面端都能正常工作
- 不影响现有功能的使用

## 后续扩展
可以考虑：
- 添加更多主题选项（如蓝色主题、绿色主题等）
- 根据系统主题自动切换
- 为其他页面也添加主题切换功能
